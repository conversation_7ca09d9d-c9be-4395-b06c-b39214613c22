'use client';

import React, { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

interface BlogCard3DProps {
  id: string;
  title: string;
  excerpt: string;
  featuredImage?: {
    url: string;
    alt?: string;
  };
  author?: {
    name: string;
    avatar?: string;
  };
  publishedAt: string;
  readTime?: number;
  category?: string;
  tags?: string[];
  className?: string;
}

const BlogCard3D: React.FC<BlogCard3DProps> = ({
  id,
  title,
  excerpt,
  featuredImage,
  author,
  publishedAt,
  readTime,
  category,
  tags,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);

  const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ['10deg', '-10deg']);
  const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ['-10deg', '10deg']);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;

    x.set(xPct);
    y.set(yPct);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
    setIsHovered(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      ref={ref}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      style={{
        rotateY: rotateY,
        rotateX: rotateX,
        transformStyle: 'preserve-3d',
      }}
      className={`relative w-full max-w-sm mx-auto cursor-pointer ${className}`}
      whileHover={{ scale: 1.02 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      {/* Card Background */}
      <motion.div
        className="relative bg-white rounded-2xl shadow-lg overflow-hidden"
        style={{
          transform: 'translateZ(50px)',
          transformStyle: 'preserve-3d',
        }}
        animate={{
          boxShadow: isHovered
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            : '0 10px 25px -3px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* Featured Image */}
        {featuredImage?.url ? (
          <div className="relative aspect-video overflow-hidden">
            <motion.div
              className="relative w-full h-full"
              animate={{ scale: isHovered ? 1.1 : 1 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
            >
              <Image
                src={featuredImage.url}
                alt={featuredImage.alt || title}
                fill
                className={`object-cover transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
                onLoad={() => setImageLoaded(true)}
              />
            </motion.div>

            {/* Category Badge */}
            {category && (
              <motion.div
                className="absolute top-4 left-4"
                style={{ transform: 'translateZ(75px)' }}
                initial={{ scale: 0, rotate: -12 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              >
                <span className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                  {category}
                </span>
              </motion.div>
            )}

            {/* Read Time */}
            {readTime && (
              <motion.div
                className="absolute top-4 right-4"
                style={{ transform: 'translateZ(75px)' }}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: isHovered ? 1 : 0, x: isHovered ? 0 : 20 }}
                transition={{ duration: 0.3 }}
              >
                <span className="bg-black/70 text-white px-2 py-1 rounded-lg text-xs backdrop-blur-sm">
                  {readTime} min read
                </span>
              </motion.div>
            )}

            {/* Gradient Overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />
          </div>
        ) : (
          <div className="aspect-video bg-gradient-to-br from-pink-100 via-purple-50 to-blue-100 flex items-center justify-center">
            <motion.div
              className="text-6xl text-gray-300"
              animate={{ 
                rotate: isHovered ? [0, 5, -5, 0] : 0 
              }}
              transition={{ duration: 0.5 }}
            >
              📝
            </motion.div>
          </div>
        )}

        {/* Content */}
        <motion.div
          className="p-6"
          style={{
            transform: 'translateZ(25px)',
            transformStyle: 'preserve-3d',
          }}
        >
          {/* Author & Date */}
          <motion.div
            className="flex items-center justify-between mb-4 text-sm text-gray-500"
            animate={{ y: isHovered ? -2 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center space-x-2">
              {author?.avatar ? (
                <Image
                  src={author.avatar}
                  alt={author.name}
                  width={24}
                  height={24}
                  className="rounded-full"
                />
              ) : (
                <div className="w-6 h-6 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                  {author?.name?.charAt(0) || 'A'}
                </div>
              )}
              <span>{author?.name || 'Anonymous'}</span>
            </div>
            <span>{formatDate(publishedAt)}</span>
          </motion.div>

          {/* Title */}
          <Link href={`/blog/${id}`}>
            <motion.h3
              className="font-bold text-xl text-gray-900 mb-3 line-clamp-2 hover:text-primary transition-colors"
              animate={{ y: isHovered ? -2 : 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              {title}
            </motion.h3>
          </Link>

          {/* Excerpt */}
          <motion.p
            className="text-gray-600 text-sm line-clamp-3 mb-4"
            animate={{ y: isHovered ? -2 : 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            {excerpt}
          </motion.p>

          {/* Tags */}
          {tags && tags.length > 0 && (
            <motion.div
              className="flex flex-wrap gap-2 mb-4"
              animate={{ y: isHovered ? -2 : 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              {tags.slice(0, 3).map((tag, index) => (
                <motion.span
                  key={tag}
                  className="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg text-xs hover:bg-primary hover:text-white transition-colors cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  #{tag}
                </motion.span>
              ))}
              {tags.length > 3 && (
                <span className="text-gray-400 text-xs">+{tags.length - 3} more</span>
              )}
            </motion.div>
          )}

          {/* Read More Button */}
          <motion.div
            animate={{ y: isHovered ? -2 : 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <Link href={`/blog/${id}`}>
              <motion.button
                className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
              >
                Read More
                <motion.span
                  className="inline-block ml-2"
                  animate={{ x: isHovered ? 2 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  →
                </motion.span>
              </motion.button>
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Floating Elements */}
      <motion.div
        className="absolute -top-2 -right-2 w-3 h-3 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full"
        style={{ transform: 'translateZ(75px)' }}
        animate={{
          scale: isHovered ? [1, 1.3, 1] : 1,
          opacity: isHovered ? [0.6, 1, 0.6] : 0.6,
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute -bottom-1 -left-1 w-2 h-2 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full"
        style={{ transform: 'translateZ(60px)' }}
        animate={{
          scale: isHovered ? [1, 1.2, 1] : 1,
          opacity: isHovered ? [0.4, 0.8, 0.4] : 0.4,
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5,
        }}
      />
    </motion.div>
  );
};

export default BlogCard3D;
