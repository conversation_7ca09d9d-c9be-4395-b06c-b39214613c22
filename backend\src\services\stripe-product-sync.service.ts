import Stripe from 'stripe';
import logger from '../config/logger.config';
import config from '../config/env.config';
import Product from '../models/product.model';

/**
 * Service to automatically sync products with <PERSON><PERSON> when they're imported from AliExpress
 * This ensures all products are available for payment processing
 */
export class StripeProductSyncService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(config.payment.stripe.secretKey, {
      apiVersion: '2024-04-10',
    });
  }

  /**
   * Create or update a product in Stripe
   * @param product - The product from our database
   * @returns Stripe product ID
   */
  async syncProductToStripe(product: any): Promise<string | null> {
    try {
      // Check if product already exists in Stripe
      let stripeProduct: Stripe.Product;
      
      if (product.stripeProductId) {
        try {
          // Try to retrieve existing product
          stripeProduct = await this.stripe.products.retrieve(product.stripeProductId);
          
          // Update existing product
          stripeProduct = await this.stripe.products.update(product.stripeProductId, {
            name: product.name,
            description: product.description,
            images: product.images?.map((img: any) => img.url || img).filter(Boolean) || [],
            metadata: {
              productId: product._id.toString(),
              sku: product.sku,
              provider: product.provider,
              externalId: product.externalId || '',
              category: product.category || '',
            },
            active: product.isActive && product.stock > 0,
          });
          
          logger.info(`Updated Stripe product: ${stripeProduct.id} for product: ${product.name}`);
        } catch (error: any) {
          if (error.code === 'resource_missing') {
            // Product doesn't exist in Stripe, create new one
            stripeProduct = await this.createStripeProduct(product);
          } else {
            throw error;
          }
        }
      } else {
        // Create new product in Stripe
        stripeProduct = await this.createStripeProduct(product);
      }

      // Create or update the price for this product
      await this.syncProductPrice(stripeProduct.id, product);

      // Update our database with the Stripe product ID
      await Product.updateOne(
        { _id: product._id },
        { 
          stripeProductId: stripeProduct.id,
          lastStripeSync: new Date()
        }
      );

      return stripeProduct.id;
    } catch (error: any) {
      logger.error(`Error syncing product to Stripe: ${product.name}`, error);
      return null;
    }
  }

  /**
   * Create a new product in Stripe
   */
  private async createStripeProduct(product: any): Promise<Stripe.Product> {
    const stripeProduct = await this.stripe.products.create({
      name: product.name,
      description: product.description,
      images: product.images?.map((img: any) => img.url || img).filter(Boolean) || [],
      metadata: {
        productId: product._id.toString(),
        sku: product.sku,
        provider: product.provider,
        externalId: product.externalId || '',
        category: product.category || '',
      },
      active: product.isActive && product.stock > 0,
    });

    logger.info(`Created Stripe product: ${stripeProduct.id} for product: ${product.name}`);
    return stripeProduct;
  }

  /**
   * Create or update price for a product in Stripe
   */
  private async syncProductPrice(stripeProductId: string, product: any): Promise<void> {
    try {
      // Create a new price (Stripe recommends creating new prices instead of updating)
      const price = await this.stripe.prices.create({
        product: stripeProductId,
        unit_amount: Math.round(product.price * 100), // Convert to cents
        currency: 'usd', // Default currency, can be made configurable
        metadata: {
          productId: product._id.toString(),
          sku: product.sku,
        },
      });

      // Deactivate old prices for this product
      const existingPrices = await this.stripe.prices.list({
        product: stripeProductId,
        active: true,
      });

      for (const existingPrice of existingPrices.data) {
        if (existingPrice.id !== price.id) {
          await this.stripe.prices.update(existingPrice.id, {
            active: false,
          });
        }
      }

      // Update our database with the current price ID
      await Product.updateOne(
        { _id: product._id },
        { stripePriceId: price.id }
      );

      logger.info(`Created Stripe price: ${price.id} for product: ${product.name}`);
    } catch (error: any) {
      logger.error(`Error creating Stripe price for product: ${product.name}`, error);
    }
  }

  /**
   * Sync all products that don't have Stripe IDs
   */
  async syncAllProducts(): Promise<{ synced: number; errors: number }> {
    try {
      const products = await Product.find({
        isActive: true,
        $or: [
          { stripeProductId: { $exists: false } },
          { stripeProductId: null },
          { stripeProductId: '' }
        ]
      });

      let synced = 0;
      let errors = 0;

      logger.info(`Starting Stripe sync for ${products.length} products`);

      for (const product of products) {
        const result = await this.syncProductToStripe(product);
        if (result) {
          synced++;
        } else {
          errors++;
        }
      }

      logger.info(`Stripe sync completed: ${synced} synced, ${errors} errors`);
      return { synced, errors };
    } catch (error: any) {
      logger.error('Error in bulk Stripe sync:', error);
      return { synced: 0, errors: 1 };
    }
  }

  /**
   * Remove a product from Stripe
   */
  async removeProductFromStripe(productId: string): Promise<boolean> {
    try {
      const product = await Product.findById(productId);
      if (!product || !product.stripeProductId) {
        return true; // Nothing to remove
      }

      // Archive the product in Stripe (can't delete products with prices)
      await this.stripe.products.update(product.stripeProductId, {
        active: false,
      });

      // Remove Stripe IDs from our database
      await Product.updateOne(
        { _id: productId },
        { 
          $unset: { 
            stripeProductId: 1,
            stripePriceId: 1 
          }
        }
      );

      logger.info(`Archived Stripe product for: ${product.name}`);
      return true;
    } catch (error: any) {
      logger.error(`Error removing product from Stripe: ${productId}`, error);
      return false;
    }
  }
}

export const stripeProductSyncService = new StripeProductSyncService();
