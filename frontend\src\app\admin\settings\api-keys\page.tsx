'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface ApiKey {
  id: string;
  name: string;
  service: string;
  description: string;
  value: string;
  isConfigured: boolean;
  isRequired: boolean;
  lastUpdated?: string;
  status: 'active' | 'inactive' | 'error';
  category: 'payment' | 'ai' | 'communication' | 'ecommerce' | 'analytics' | 'storage';
  helpUrl?: string;
}

interface ApiKeyCategory {
  name: string;
  description: string;
  icon: string;
  keys: ApiKey[];
}

export default function ApiKeysManagementPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [showValues, setShowValues] = useState<{ [key: string]: boolean }>({});
  const [testingKey, setTestingKey] = useState<string | null>(null);

  // Fetch API keys from backend
  useEffect(() => {
    fetchApiKeys();
  }, []);

  const fetchApiKeys = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch API keys');
      }

      const data = await response.json();

      if (data.success) {
        // Transform backend data to frontend format
        const transformedKeys = transformBackendApiKeys(data.apiKeys);
        setApiKeys(transformedKeys);
      } else {
        console.error('Failed to fetch API keys:', data.error);
      }
    } catch (error) {
      console.error('Error fetching API keys:', error);
      // Fallback to empty array on error
      setApiKeys([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Transform backend API keys format to frontend format
  const transformBackendApiKeys = (backendKeys: any): ApiKey[] => {
    const transformedKeys: ApiKey[] = [];

    // Transform each service's keys
    Object.entries(backendKeys).forEach(([service, keys]: [string, any]) => {
      Object.entries(keys).forEach(([keyName, value]: [string, any]) => {
        // Skip non-key fields
        if (typeof value !== 'string') return;

        const keyId = `${service}_${keyName}`;
        const isConfigured = Boolean(value && !value.includes('***'));

        transformedKeys.push({
          id: keyId,
          name: formatKeyName(keyName),
          service: formatServiceName(service),
          description: getKeyDescription(service, keyName),
          value: value || '',
          isConfigured,
          isRequired: getKeyRequirement(service, keyName),
          lastUpdated: new Date().toISOString(),
          status: isConfigured ? 'active' : 'inactive',
          category: getKeyCategory(service),
          helpUrl: getHelpUrl(service, keyName)
        });
      });
    });

    return transformedKeys;
  };

  // Helper functions for transformation
  const formatKeyName = (keyName: string): string => {
    return keyName
      .split(/[_-]/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatServiceName = (service: string): string => {
    const serviceNames: { [key: string]: string } = {
      'aliexpress': 'AliExpress',
      'stripe': 'Stripe',
      'paypal': 'PayPal',
      'whatsapp': 'WhatsApp Business',
      'openai': 'OpenAI',
      'google': 'Google',
      'instagram': 'Instagram',
      'email': 'Email',
      'ngrok': 'Ngrok'
    };
    return serviceNames[service] || service.charAt(0).toUpperCase() + service.slice(1);
  };

  const getKeyDescription = (service: string, keyName: string): string => {
    const descriptions: { [key: string]: string } = {
      'aliexpress_appKey': 'API key for product synchronization from AliExpress',
      'aliexpress_appSecret': 'Secret key for AliExpress API authentication',
      'stripe_publicKey': 'Publishable key for Stripe frontend integration',
      'stripe_secretKey': 'Secret key for processing payments via Stripe',
      'stripe_webhookSecret': 'Webhook secret for Stripe event verification',
      'paypal_clientId': 'Client ID for PayPal payment integration',
      'paypal_clientSecret': 'Client secret for PayPal payment integration',
      'whatsapp_accessToken': 'Access token for WhatsApp Business API integration',
      'whatsapp_phoneNumberId': 'Phone number ID for sending WhatsApp messages',
      'whatsapp_businessAccountId': 'Business account ID for WhatsApp',
      'whatsapp_webhookVerifyToken': 'Verify token for WhatsApp webhook verification',
      'openai_apiKey': 'API key for ChatGPT, voice processing, and AI features',
      'google_speechToTextKey': 'API key for Google Speech-to-Text service',
      'google_textToSpeechKey': 'API key for Google Text-to-Speech service',
      'google_vertexAIKey': 'API key for Google Vertex AI and Gemini models',
      'instagram_accessToken': 'Access token for Instagram Business API',
      'instagram_businessAccountId': 'Business account ID for Instagram',
      'email_sendgridKey': 'API key for SendGrid email service',
      'email_fromEmail': 'Default from email address',
      'email_fromName': 'Default from name for emails',
      'ngrok_authToken': 'Auth token for Ngrok tunneling service',
      'ngrok_apiKey': 'API key for Ngrok management'
    };
    return descriptions[`${service}_${keyName}`] || `${formatKeyName(keyName)} for ${formatServiceName(service)}`;
  };

  const getKeyRequirement = (service: string, keyName: string): boolean => {
    const requiredKeys = [
      'aliexpress_appKey', 'aliexpress_appSecret',
      'stripe_secretKey', 'stripe_publicKey',
      'whatsapp_accessToken', 'whatsapp_phoneNumberId',
      'openai_apiKey'
    ];
    return requiredKeys.includes(`${service}_${keyName}`);
  };

  const getKeyCategory = (service: string): string => {
    const categories: { [key: string]: string } = {
      'aliexpress': 'ecommerce',
      'stripe': 'payment',
      'paypal': 'payment',
      'whatsapp': 'communication',
      'openai': 'ai',
      'google': 'ai',
      'instagram': 'analytics',
      'email': 'communication',
      'ngrok': 'storage'
    };
    return categories[service] || 'other';
  };

  const getHelpUrl = (service: string, keyName: string): string => {
    const urls: { [key: string]: string } = {
      'aliexpress': 'https://developers.aliexpress.com/',
      'stripe': 'https://stripe.com/docs/keys',
      'paypal': 'https://developer.paypal.com/docs/api/overview/',
      'whatsapp': 'https://developers.facebook.com/docs/whatsapp',
      'openai': 'https://platform.openai.com/api-keys',
      'google': 'https://cloud.google.com/vertex-ai/docs',
      'instagram': 'https://developers.facebook.com/docs/instagram-api',
      'email': 'https://sendgrid.com/docs/api-reference/',
      'ngrok': 'https://ngrok.com/docs'
    };
    return urls[service] || '';
  };

  const groupedKeys: ApiKeyCategory[] = [
    {
      name: 'Payment Services',
      description: 'Payment processing and financial transactions',
      icon: '💳',
      keys: apiKeys.filter(key => key.category === 'payment')
    },
    {
      name: 'AI & Machine Learning',
      description: 'Artificial intelligence and language processing',
      icon: '🤖',
      keys: apiKeys.filter(key => key.category === 'ai')
    },
    {
      name: 'Communication',
      description: 'Messaging and customer communication',
      icon: '💬',
      keys: apiKeys.filter(key => key.category === 'communication')
    },
    {
      name: 'E-commerce',
      description: 'Product sourcing and marketplace integration',
      icon: '🛒',
      keys: apiKeys.filter(key => key.category === 'ecommerce')
    },
    {
      name: 'Analytics',
      description: 'Tracking and performance monitoring',
      icon: '📊',
      keys: apiKeys.filter(key => key.category === 'analytics')
    },
    {
      name: 'Storage & Media',
      description: 'File storage and media management',
      icon: '☁️',
      keys: apiKeys.filter(key => key.category === 'storage')
    }
  ];

  const handleUpdateKey = async (keyId: string, newValue: string) => {
    try {
      // Extract service and key name from keyId
      const [service, ...keyParts] = keyId.split('_');
      const keyName = keyParts.join('_');

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys/${service}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          [keyName]: newValue
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update API key');
      }

      const data = await response.json();

      if (data.success) {
        // Update local state
        setApiKeys(prev => prev.map(key =>
          key.id === keyId
            ? {
                ...key,
                value: newValue,
                isConfigured: newValue.length > 0,
                status: newValue.length > 0 ? 'active' : 'inactive',
                lastUpdated: new Date().toISOString()
              }
            : key
        ));
        setEditingKey(null);

        // Show success message
        console.log('API key updated successfully');
      } else {
        throw new Error(data.error || 'Failed to update API key');
      }
    } catch (error) {
      console.error('Error updating API key:', error);
      // Show error message to user
      alert('Failed to update API key. Please try again.');
    }
  };

  const handleTestKey = async (keyId: string) => {
    try {
      setTestingKey(keyId);

      // Extract service from keyId
      const [service] = keyId.split('_');

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys/${service}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to test API key');
      }

      const data = await response.json();

      // Update status based on test result
      setApiKeys(prev => prev.map(key =>
        key.id === keyId
          ? { ...key, status: data.success ? 'active' : 'error' }
          : key
      ));

      // Show result message
      if (data.success) {
        console.log('API key test successful:', data.message);
      } else {
        console.error('API key test failed:', data.message);
      }

    } catch (error) {
      console.error('Error testing API key:', error);
      // Update status to error
      setApiKeys(prev => prev.map(key =>
        key.id === keyId
          ? { ...key, status: 'error' }
          : key
      ));
    } finally {
      setTestingKey(null);
    }
  };

  const toggleShowValue = (keyId: string) => {
    setShowValues(prev => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '✅';
      case 'inactive': return '⚪';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const maskValue = (value: string) => {
    if (!value) return 'Not configured';
    if (value.length <= 8) return '*'.repeat(value.length);
    return value.substring(0, 4) + '*'.repeat(Math.max(4, value.length - 8)) + value.substring(value.length - 4);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading API keys...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">API Keys Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Securely manage all your external service API keys and credentials
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export Configuration
            </button>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Security Notice</h3>
              <p className="text-sm text-yellow-700 mt-1">
                API keys are sensitive credentials. Never share them publicly or commit them to version control.
                All keys are encrypted and stored securely in environment variables.
              </p>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-sm">✅</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Configured</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <span className="text-red-600 text-sm">❌</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Missing</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => !k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <span className="text-yellow-600 text-sm">⚠️</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Required</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => k.isRequired && !k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🔧</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Services</p>
                <p className="text-2xl font-bold text-gray-900">{groupedKeys.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* API Keys by Category */}
        {groupedKeys.map((category, categoryIndex) => (
          <motion.div
            key={category.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: categoryIndex * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{category.icon}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                    <p className="text-sm text-gray-500">{category.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">
                    {category.keys.filter(k => k.isConfigured).length}/{category.keys.length} configured
                  </span>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200">
              {category.keys.map((key, keyIndex) => (
                <motion.div
                  key={key.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (categoryIndex * 0.1) + (keyIndex * 0.05) }}
                  className="p-6 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getStatusIcon(key.status)}</span>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900">{key.name}</h4>
                            {key.isRequired && (
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                Required
                              </span>
                            )}
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(key.status)}`}>
                              {key.status.charAt(0).toUpperCase() + key.status.slice(1)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{key.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="text-xs text-gray-400">
                              Service: {key.service}
                            </span>
                            <span className="text-xs text-gray-400">
                              Last updated: {formatDate(key.lastUpdated)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* API Key Value */}
                      <div className="mt-4">
                        {editingKey === key.id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              defaultValue={key.value}
                              placeholder="Enter API key..."
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdateKey(key.id, e.currentTarget.value);
                                } else if (e.key === 'Escape') {
                                  setEditingKey(null);
                                }
                              }}
                              autoFocus
                            />
                            <button
                              onClick={() => {
                                const input = document.querySelector(`input[defaultValue="${key.value}"]`) as HTMLInputElement;
                                if (input) handleUpdateKey(key.id, input.value);
                              }}
                              className="px-3 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => setEditingKey(null)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                            >
                              Cancel
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <code className="flex-1 px-3 py-2 bg-gray-100 rounded-md text-sm font-mono">
                              {showValues[key.id] ? key.value || 'Not configured' : maskValue(key.value)}
                            </code>
                            <button
                              onClick={() => toggleShowValue(key.id)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                              title={showValues[key.id] ? 'Hide value' : 'Show value'}
                            >
                              {showValues[key.id] ? '🙈' : '👁️'}
                            </button>
                            <button
                              onClick={() => setEditingKey(key.id)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                            >
                              Edit
                            </button>
                            {key.isConfigured && (
                              <button
                                onClick={() => handleTestKey(key.id)}
                                disabled={testingKey === key.id}
                                className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50"
                              >
                                {testingKey === key.id ? 'Testing...' : 'Test'}
                              </button>
                            )}
                            {key.helpUrl && (
                              <a
                                href={key.helpUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                                title="View documentation"
                              >
                                📚
                              </a>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Need Help?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Getting API Keys</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Visit the service provider's developer portal</li>
                <li>• Create an account or log in</li>
                <li>• Navigate to API keys or credentials section</li>
                <li>• Generate new API keys for your application</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Security Best Practices</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Never share API keys publicly</li>
                <li>• Use environment variables for storage</li>
                <li>• Rotate keys regularly</li>
                <li>• Monitor usage and set up alerts</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
  );
}