'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  CreditCardIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

interface PaymentConfig {
  enabled: boolean;
  providers: {
    stripe: {
      enabled: boolean;
      publicKey: string;
      secretKey: string;
      webhookSecret: string;
    };
    paypal: {
      enabled: boolean;
      clientId: string;
      clientSecret: string;
      sandbox: boolean;
    };
    mercadoPago: {
      enabled: boolean;
      publicKey: string;
      accessToken: string;
      sandbox: boolean;
    };
  };
  currency: {
    primary: string;
    supported: string[];
    autoConvert: boolean;
  };
  fees: {
    processingFee: number;
    internationalFee: number;
    refundFee: number;
  };
  security: {
    requireCVV: boolean;
    require3DS: boolean;
    fraudDetection: boolean;
    maxAttempts: number;
  };
}

export default function PaymentConfigPage() {
  const [config, setConfig] = useState<PaymentConfig>({
    enabled: true,
    providers: {
      stripe: {
        enabled: true,
        publicKey: '',
        secretKey: '',
        webhookSecret: ''
      },
      paypal: {
        enabled: false,
        clientId: '',
        clientSecret: '',
        sandbox: true
      },
      mercadoPago: {
        enabled: false,
        publicKey: '',
        accessToken: '',
        sandbox: true
      }
    },
    currency: {
      primary: 'USD',
      supported: ['USD', 'EUR', 'CAD', 'MXN'],
      autoConvert: true
    },
    fees: {
      processingFee: 2.9,
      internationalFee: 1.5,
      refundFee: 0
    },
    security: {
      requireCVV: true,
      require3DS: true,
      fraudDetection: true,
      maxAttempts: 3
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);

  const currencyOptions = [
    { value: 'USD', label: 'US Dollar (USD)' },
    { value: 'EUR', label: 'Euro (EUR)' },
    { value: 'CAD', label: 'Canadian Dollar (CAD)' },
    { value: 'MXN', label: 'Mexican Peso (MXN)' },
    { value: 'GBP', label: 'British Pound (GBP)' }
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/payment`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching payment config:', error);
      toast.error('Failed to load payment configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/payment`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('Payment configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating payment config:', error);
      toast.error('Failed to update payment configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async (provider: string) => {
    setTesting(provider);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys/${provider}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(`${provider} connection successful`);
      } else {
        toast.error(`${provider} connection failed: ${result.message}`);
      }
    } catch (error) {
      console.error(`Error testing ${provider}:`, error);
      toast.error(`Failed to test ${provider} connection`);
    } finally {
      setTesting(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure payment providers, currencies, and security settings
          </p>
        </div>
      </div>

      {/* Payment Providers */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <CreditCardIcon className="h-5 w-5 mr-2" />
            Payment Providers
          </h2>
        </div>
        
        <div className="p-6 space-y-8">
          {/* Stripe */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-sm">S</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Stripe</h3>
                  <p className="text-sm text-gray-500">Credit cards, digital wallets</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleTest('stripe')}
                  disabled={testing === 'stripe'}
                  className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
                >
                  {testing === 'stripe' ? 'Testing...' : 'Test'}
                </button>
                <button
                  onClick={() => setConfig(prev => ({
                    ...prev,
                    providers: {
                      ...prev.providers,
                      stripe: { ...prev.providers.stripe, enabled: !prev.providers.stripe.enabled }
                    }
                  }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.providers.stripe.enabled ? 'bg-green-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.providers.stripe.enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
            
            {config.providers.stripe.enabled && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Publishable Key
                  </label>
                  <input
                    type="text"
                    value={config.providers.stripe.publicKey}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      providers: {
                        ...prev.providers,
                        stripe: { ...prev.providers.stripe, publicKey: e.target.value }
                      }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary text-sm"
                    placeholder="pk_test_..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Secret Key
                  </label>
                  <input
                    type="password"
                    value={config.providers.stripe.secretKey}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      providers: {
                        ...prev.providers,
                        stripe: { ...prev.providers.stripe, secretKey: e.target.value }
                      }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary text-sm"
                    placeholder="sk_test_..."
                  />
                </div>
              </div>
            )}
          </div>

          {/* PayPal */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-sm">P</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">PayPal</h3>
                  <p className="text-sm text-gray-500">PayPal payments</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleTest('paypal')}
                  disabled={testing === 'paypal'}
                  className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
                >
                  {testing === 'paypal' ? 'Testing...' : 'Test'}
                </button>
                <button
                  onClick={() => setConfig(prev => ({
                    ...prev,
                    providers: {
                      ...prev.providers,
                      paypal: { ...prev.providers.paypal, enabled: !prev.providers.paypal.enabled }
                    }
                  }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.providers.paypal.enabled ? 'bg-green-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.providers.paypal.enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
