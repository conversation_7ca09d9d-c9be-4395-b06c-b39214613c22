'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  ServerIcon,
  ChatBubbleLeftRightIcon,
  CogIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  MicrophoneIcon,
  LanguageIcon
} from '@heroicons/react/24/outline';

interface ChatbotConfig {
  enabled: boolean;
  name: string;
  personality: string;
  language: string;
  responseTime: number;
  maxTokens: number;
  temperature: number;
  fallbackMessage: string;
  greetingMessage: string;
  features: {
    voiceResponse: boolean;
    imageAnalysis: boolean;
    productRecommendations: boolean;
    orderTracking: boolean;
    multiLanguage: boolean;
  };
  knowledgeBase: {
    productCatalog: boolean;
    faq: boolean;
    policies: boolean;
    customData: string;
  };
}

export default function ChatbotConfigPage() {
  const [config, setConfig] = useState<ChatbotConfig>({
    enabled: true,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    personality: 'friendly',
    language: 'auto',
    responseTime: 2,
    maxTokens: 500,
    temperature: 0.7,
    fallbackMessage: 'I apologize, but I\'m having trouble understanding your request. Please try rephrasing or contact our support team.',
    greetingMessage: 'Hello! I\'m your Pelucas Chic assistant. How can I help you find the perfect wig today? 💕',
    features: {
      voiceResponse: true,
      imageAnalysis: true,
      productRecommendations: true,
      orderTracking: true,
      multiLanguage: true
    },
    knowledgeBase: {
      productCatalog: true,
      faq: true,
      policies: true,
      customData: ''
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  const personalityOptions = [
    { value: 'friendly', label: 'Friendly & Helpful' },
    { value: 'professional', label: 'Professional' },
    { value: 'casual', label: 'Casual & Fun' },
    { value: 'expert', label: 'Expert & Knowledgeable' }
  ];

  const languageOptions = [
    { value: 'auto', label: 'Auto-detect' },
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'both', label: 'Bilingual (EN/ES)' }
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/chatbot`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching chatbot config:', error);
      toast.error('Failed to load chatbot configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/chatbot`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('Chatbot configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating chatbot config:', error);
      toast.error('Failed to update chatbot configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys/chatbot/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error testing chatbot:', error);
      toast.error('Failed to test chatbot connection');
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Chatbot Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure your AI-powered chatbot for customer support and sales assistance
          </p>
        </div>
        <button
          onClick={handleTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {testing ? (
            <>
              <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
              Testing...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Test Chatbot
            </>
          )}
        </button>
      </div>

      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ServerIcon className="h-5 w-5 mr-2" />
            Basic Settings
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable AI Chatbot</h3>
              <p className="text-sm text-gray-500">
                Turn on/off the AI chatbot for customer interactions
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Chatbot Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chatbot Name
              </label>
              <input
                type="text"
                value={config.name}
                onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Assistant Name"
              />
            </div>

            {/* Personality */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Personality
              </label>
              <select
                value={config.personality}
                onChange={(e) => setConfig(prev => ({ ...prev, personality: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                {personalityOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Language */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <LanguageIcon className="inline h-4 w-4 mr-1" />
                Language
              </label>
              <select
                value={config.language}
                onChange={(e) => setConfig(prev => ({ ...prev, language: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                {languageOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Response Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Response Delay (seconds)
              </label>
              <input
                type="number"
                value={config.responseTime}
                onChange={(e) => setConfig(prev => ({ ...prev, responseTime: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                min="0"
                max="10"
              />
              <p className="mt-1 text-sm text-gray-500">
                Delay before responding (0-10 seconds)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
