import { IProductAdapter } from './product.adapter.interface';
import { IProduct } from '../../models/product.model';
import Product from '../../models/product.model';
import AppError from '../../utils/errors/AppError';
import logger from '../../config/logger.config';
import Category from '../../models/category.model';
import StoreToken from '../../models/store-token.model';
import axios from 'axios';
import config from '../../config/env.config';
import crypto from 'crypto';
import querystring from 'querystring';
import { stripeProductSyncService } from '../stripe-product-sync.service';

// Define types for AliExpress API responses
interface AliExpressTokenResponse {
  token?: string;
  [key: string]: any;
}

interface AliExpressErrorResponse {
  error_response?: {
    code?: string;
    msg?: string;
    sub_code?: string;
    sub_msg?: string;
  };
  code?: string;
  msg?: string;
  sub_code?: string;
  sub_msg?: string;
  [key: string]: any;
}

interface AliExpressProductResponse {
  product_id?: string;
  external_id?: string;
  title?: string;
  description?: string;
  price?: {
    amount?: number;
    currency_code?: string;
  };
  amount?: number;
  currency_code?: string;
  stock?: number;
  images?: string[];
  variants?: Array<{
    sku: string;
    price: number;
    stock: number;
    properties?: Record<string, string>;
  }>;
  [key: string]: any;
}

/**
 * AliExpress product adapter
 * Note: This is a real implementation that integrates with the actual AliExpress API.
 */
export class AliExpressProductAdapter implements IProductAdapter {
  // Store IDs from AliExpress to fetch products from
  private storeIds: string[] = [];
  
  constructor() {
    // Initialize store IDs from environment configuration
    if (config.aliexpress?.stores?.ids) {
      this.storeIds = config.aliexpress.stores.ids.map((id: string) => id.trim());
    }
  }
  /**
   * Generate signature for AliExpress API authentication
   * @param params Request parameters
   * @param secret App secret
   * @returns Signature string
   */
  private generateSignature(params: Record<string, any>, secret: string): string {
    // Sort parameters alphabetically by key
    const sortedKeys = Object.keys(params).sort();
    
    // Concatenate key-value pairs
    let signatureString = '';
    sortedKeys.forEach(key => {
      const value = params[key];
      if (value !== undefined && value !== null) {
        signatureString += `${key}${value}`;
      }
    });
    
    // Prepend app secret
    signatureString = secret + signatureString;
    
    // Generate MD5 hash
    return crypto.createHash('md5').update(signatureString).digest('hex');
  }
  
  /**
   * Get access token from StoreToken model or refresh if needed
   * @param storeId AliExpress store ID to get token for
   * @returns Valid access token
   */
  private async getAccessToken(storeId?: string): Promise<string> {
    try {
      // If no storeId is provided, use the first available storeId from config
      const targetStoreId = storeId || (this.storeIds.length > 0 ? this.storeIds[0] : undefined);
      
      if (!targetStoreId) {
        throw new Error('No store ID provided or configured');
      }
      
      // Try to find an existing token for this store
      const storeToken = await StoreToken.findOne({ 
        storeId: targetStoreId, 
        provider: 'aliexpress',
        isActive: true 
      });
      
      if (!storeToken) {
        logger.warn(`No token found for AliExpress store ${targetStoreId}. OAuth authorization needed.`);
        throw new AppError(`No OAuth token available for AliExpress store ${targetStoreId}. Store owner must authorize via OAuth.`, 401);
      }
      
      // Check if token is expired and needs refresh
      if (storeToken.isExpired()) {
        logger.info(`Refreshing expired token for AliExpress store ${targetStoreId}`);
        
        const { appKey, appSecret, apiUrl } = config.aliexpress;
        if (!appKey || !appSecret) {
          throw new Error('Missing AliExpress API credentials');
        }
        
        // Prepare parameters for token refresh request
        const refreshParams = {
          app_key: appKey,
          refresh_token: storeToken.refreshToken,
          grant_type: 'refresh_token',
          timestamp: new Date().toISOString(),
          sign_method: 'md5'
        };
        
        // Generate signature
        const signature = this.generateSignature(refreshParams, appSecret);
        
        // Make token refresh request
        const response = await axios.post<AliExpressTokenResponse>(
          `${apiUrl || 'https://api.aliexpress.com'}/auth/token/refresh`,
          null,
          {
            params: {
              ...refreshParams,
              sign: signature
            }
          }
        );
        
        const data = response.data;
        if (!data || !data.access_token) {
          throw new Error('Failed to refresh token: Invalid response');
        }
        
        // Update token in database
        const expiresIn = data.expires_in || 7200; // Default to 2 hours if not specified
        storeToken.accessToken = data.access_token;
        storeToken.refreshToken = data.refresh_token || storeToken.refreshToken;
        storeToken.expiresAt = new Date(Date.now() + expiresIn * 1000);
        storeToken.lastRefreshed = new Date();
        await storeToken.save();
        
        return storeToken.accessToken;
      }
      
      // Return existing valid token
      return storeToken.accessToken;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error getting AliExpress access token: ${errorMessage}`);
      
      // If this is already an AppError, rethrow it
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError(`Failed to authenticate with AliExpress: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Make API call to AliExpress
   * @param endpoint API endpoint path
   * @param method HTTP method (GET/POST)
   * @param params Request parameters
   * @param storeId Optional store ID to use for authentication
   * @returns API response data
   */
  private async callAliExpressAPI(endpoint: string, method: string, params: Record<string, any> = {}, storeId?: string): Promise<any> {
    try {
      logger.info(`AliExpress API call: ${method} ${endpoint}`);
      
      const { appKey, appSecret, apiUrl } = config.aliexpress;
      if (!appKey || !appSecret) {
        throw new Error('Missing AliExpress API credentials');
      }
      
      // Get access token for the specified store
      const accessToken = await this.getAccessToken(storeId);
      
      // Prepare request parameters
      const requestParams = {
        ...params,
        app_key: appKey,
        session: accessToken,
        timestamp: new Date().toISOString(),
        sign_method: 'md5',
      };
      
      // Generate signature
      const signature = this.generateSignature(requestParams, appSecret);
      
      // Add signature to params
      const finalParams = {
        ...requestParams,
        sign: signature
      };
      
      // Make API request
      let response: any;
      const url = `${apiUrl}/${endpoint}`;
      
      if (method === 'GET') {
        response = await axios.get<AliExpressErrorResponse>(url, { params: finalParams });
      } else if (method === 'POST') {
        response = await axios.post<AliExpressErrorResponse>(url, querystring.stringify(finalParams));
      } else {
        throw new Error(`Unsupported method: ${method}`);
      }
      
      // Check for API errors in response
      const responseData = response.data as AliExpressErrorResponse;
      if (responseData && responseData.error_response) {
        const apiError = responseData.error_response;
        logger.error(`AliExpress API error: ${apiError.msg || 'Unknown API error'}`);
        throw new Error(`AliExpress API error: ${apiError.msg || 'Unknown API error'}`);
      }
      
      return responseData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error calling AliExpress API (${endpoint}): ${errorMessage}`);
      
      // Throw a proper error instead of returning mock data
      throw new AppError(`AliExpress API error (${endpoint}): ${errorMessage}`, 500);
    }
  }
  
  /**
   * Map AliExpress product to our product model
   * This function handles various possible property names and formats from AliExpress API
   * @param aliProduct The AliExpress product data to map
   * @param storeId The AliExpress store ID this product belongs to
   */
  private mapToProductModel(aliProduct: any, storeId?: string): any {
    const productId = aliProduct.external_id || aliProduct.product_id || aliProduct.id;
    if (!productId) {
      logger.warn('AliExpress product missing ID', { product: aliProduct });
    }

    return {
      name: aliProduct.name || aliProduct.title || aliProduct.subject || 'Unknown Product',
      sku: `ALI-${productId || Math.random().toString(36).substring(2, 15)}`,
      description: aliProduct.description || aliProduct.desc || aliProduct.product_description || 'Imported from AliExpress',
      price: aliProduct.price?.amount || aliProduct.price || aliProduct.amount || aliProduct.sale_price || 0,
      category: 'Wigs', // Default category
      stock: aliProduct.stock,
      provider: 'aliexpress',
      externalId: productId,
      images: aliProduct.images || [],
      aliExpressSource: {
        id: productId,
        url: aliProduct.product_url || aliProduct.url || `https://www.aliexpress.com/item/${productId}.html`,
        originalPrice: aliProduct.price?.amount || aliProduct.price || aliProduct.amount || 0,
        supplierName: aliProduct.seller_name || aliProduct.supplier_name || aliProduct.shop_name,
        supplierScore: aliProduct.seller_rating || aliProduct.supplier_rating,
        lastSyncDate: new Date(),
        storeId: storeId // Include the store ID in the aliExpressSource object
      }
    };
  }
  
  /**
   * Search products with full-text search, fuzzy matching, and result highlighting
   * @param query Search query
   * @param options Search options
   */
  async searchProducts(query: string, options?: {
    fuzzy?: boolean;
    highlight?: boolean;
    fields?: string;
    page?: number;
    limit?: number;
    storeId?: string; // Add storeId parameter
  }): Promise<{
    products: any[];
    totalCount: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      // Process options
      const { 
        fuzzy = true,
        highlight = false,
        fields,
        page = 1, 
        limit = 20,
        storeId // Get storeId from options
      } = options || {};
      
      logger.info('Searching products from AliExpress API', {
        query,
        options,
        storeIds: this.storeIds
      });
      
      // Set up API parameters
      const apiParams: Record<string, any> = {
        page_no: page,
        page_size: limit,
        keywords: query,
        // Include store IDs from config to only get products from our stores
        store_ids: this.storeIds?.join(',') || ''
      };
      
      // Add fuzzy search if enabled
      if (fuzzy) {
        apiParams.fuzzy_search = true;
      }
      
      // Call the AliExpress API directly using the product search endpoint
      // Note: AliExpress API may not support highlighting directly, but we'll try to handle that on our side
      const response = await this.callAliExpressAPI('ds/product/search', 'POST', apiParams, storeId);
      
      // Handle empty or invalid response
      if (!response || !response.data || !Array.isArray(response.data.products)) {
        // Try fallback to list API with keyword filter as search might not be directly supported
        logger.warn('AliExpress API search endpoint returned invalid data structure, trying fallback to list with keyword filter');
        
        const fallbackParams: Record<string, any> = {
          page_no: page,
          page_size: limit,
          keywords: query,
          store_ids: this.storeIds?.join(',') || ''
        };
        
        const fallbackResponse = await this.callAliExpressAPI('ds/product/list', 'POST', fallbackParams, storeId);
        
        if (!fallbackResponse || !fallbackResponse.data || !Array.isArray(fallbackResponse.data.products)) {
          logger.warn('AliExpress API fallback search returned invalid data structure', { 
            responseStructure: fallbackResponse ? Object.keys(fallbackResponse) : null
          });
          
          // Return empty results in the required format
          return {
            products: [],
            totalCount: 0,
            page,
            limit,
            totalPages: 0
          };
        }
        
        // Use the fallback response
        response.data = fallbackResponse.data;
      }
      
      // Map the AliExpress API response to our product model
      let products = await Promise.all(
        response.data.products.map(async (productData: any) => {
          // Use the specific storeId or the one from apiParams if available
          const productStoreId = storeId || (apiParams.store_ids ? apiParams.store_ids.split(',')[0] : undefined);
          return await this.mapToProductModel(productData, productStoreId);
        })
      );
      
      // If highlighting is requested, we need to add it manually as the API may not support it
      if (highlight && query && products.length > 0) {
        // Simple highlighting by wrapping matching text with <mark> tags
        const highlightText = (text: string, searchTerm: string): string => {
          if (!text || typeof text !== 'string') return text;
          const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
          return text.replace(regex, match => `<mark>${match}</mark>`);
        };
        
        // Apply highlighting to relevant fields
        products = products.map(product => {
          if (product.name) product.name = highlightText(product.name, query);
          if (product.description) product.description = highlightText(product.description, query);
          return product;
        });
      }
      
      // Get total count and calculate total pages
      const totalCount = response.data.total_count || products.length;
      const totalPages = Math.ceil(totalCount / limit);
      
      logger.info('Successfully searched products from AliExpress API', { 
        query,
        resultCount: products.length,
        totalCount,
        totalPages
      });
      
      return {
        products,
        totalCount,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Error searching products from AliExpress API', { 
        query, 
        error: errorMessage 
      });
      throw new AppError(`Failed to search products: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Get all products directly from AliExpress API
   */
  async getAllProducts(options: {
    filters?: Record<string, any>;
    sort?: string;
    page?: number;
    limit?: number;
    fields?: string;
  } = {}): Promise<{
    products: any[];
    totalCount: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      // Process options
      const { 
        filters = {}, 
        page = 1, 
        limit = 20, 
        sort = 'create_at', 
        fields 
      } = options;
      
      logger.info('Fetching all products from AliExpress API', {
        options,
        storeIds: this.storeIds
      });
      
      // Set up API parameters
      const sortBy = sort?.startsWith('-') ? sort.substring(1) : sort;
      const sortOrder = sort?.startsWith('-') ? 'desc' : 'asc';
      
      // Define API params with all possible properties to handle TypeScript type checking
      const apiParams: Record<string, any> = {
        page_no: page,
        page_size: limit,
        sort: sortBy,
        sort_direction: sortOrder,
        // Include store IDs from config to only get products from our stores
        store_ids: this.storeIds?.join(',') || ''
      };
      
      // Add any supported filters from the options
      if (filters) {
        if (filters.category) apiParams.category_id = filters.category;
        if (filters.keyword) apiParams.keywords = filters.keyword;
        if (filters.minPrice) apiParams.min_price = filters.minPrice;
        if (filters.maxPrice) apiParams.max_price = filters.maxPrice;
        if (filters.status) apiParams.status = filters.status;
      }
      
      // If a specific store filter is provided, use it for the API call
      const storeId = filters.storeId as string || (this.storeIds.length > 0 ? this.storeIds[0] : undefined);
      
      // Call the AliExpress API directly with the appropriate storeId
      const response = await this.callAliExpressAPI('ds/product/list', 'POST', apiParams, storeId);
      
      // Handle empty or invalid response
      if (!response || !response.data || !Array.isArray(response.data.products)) {
        logger.warn('AliExpress API returned invalid data structure', { 
          responseStructure: response ? Object.keys(response) : null
        });
        
        // Return empty results in the required format
        return {
          products: [],
          totalCount: 0,
          page,
          limit,
          totalPages: 0
        };
      }
      
      // Map the AliExpress API response to our product model
      const products = await Promise.all(
        response.data.products.map(async (productData: any) => {
          return await this.mapToProductModel(productData);
        })
      );
      
      // Get total count and calculate total pages
      const totalCount = response.data.total_count || products.length;
      const totalPages = Math.ceil(totalCount / limit);
      
      logger.info('Successfully fetched products from AliExpress API', { 
        resultCount: products.length,
        totalCount,
        totalPages
      });
      
      return {
        products,
        totalCount,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Error fetching products from AliExpress API', { error: errorMessage });
      throw new AppError(`Failed to get products: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Get a product by ID
   * Fetches from local database and refreshes from AliExpress API if it's an AliExpress product
   */
  async getProductById(id: string, options?: {
    includeRelated?: boolean;
    includeReviews?: boolean;
  }): Promise<any> {
    try {
      // Try to find in local database first
      const product = await Product.findById(id);
      
      if (!product) {
        throw new AppError(`No product found with id: ${id}`, 404);
      }
      
      // If it's an AliExpress product, fetch latest data from their API
      if (product.provider === 'aliexpress' && product.externalId) {
        logger.info(`Refreshing product ${id} (External ID: ${product.externalId}) from AliExpress API`);
        
        try {
          // Call AliExpress API to get fresh product data
          // Use the store ID from the product's aliExpressSource if available
          const storeId = product.aliExpressSource?.storeId || undefined;
          
          // Using the product detail endpoint for single product fetch
          const freshData = await this.callAliExpressAPI('ds/product/get', 'GET', {
            product_id: product.externalId
          }, storeId);
          
          if (freshData && (freshData.product || freshData.product_detail)) {
            // Extract the product details from the API response
            const apiProduct = freshData.product || freshData.product_detail;
            
            // Update fields that might change in AliExpress (e.g., price, stock)
            // Preserving our own fields like category assignments and custom data
            if (apiProduct.price) product.price = apiProduct.price.amount || apiProduct.price;
            if (apiProduct.stock) product.stock = apiProduct.stock;
            if (apiProduct.description) product.description = apiProduct.description;
            if (apiProduct.images) product.images = apiProduct.images;
            
            // Save the updated product info
            await product.save();
            logger.info(`Updated product ${id} with fresh data from AliExpress API`);
          }
        } catch (apiError) {
          // Log the error but don't fail the request - return the local data we have
          logger.warn(`Failed to refresh product ${id} from AliExpress API: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`);
        }
      }
      
      // Handle options
      const includeRelated = options?.includeRelated || false;
      const includeReviews = options?.includeReviews || false;
      
      // We already have a resolved product object from the above await
      // Only proceed with population if the product exists
      if (product) {
        try {
          // Populate related data if requested
          if (includeRelated && typeof product.populate === 'function') {
            // First try to populate related products if they exist
            await Promise.all([
              product.populate?.('relatedProducts'),
              product.populate?.('relatedIds'),
              product.populate?.('related')
            ]);
          }
          
          // Populate reviews if requested
          if (includeReviews && typeof product.populate === 'function') {
            // Try common field names for reviews
            await Promise.all([
              product.populate?.('reviews'),
              product.populate?.('reviewIds')
            ]);
          }
        } catch (populateError) {
          // Log but don't fail if population fails (e.g., field doesn't exist)
          logger.warn(`Could not populate related data: ${populateError instanceof Error ? populateError.message : 'Unknown error'}`);
        }
      }
      
      return product;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error in getProductById: ${errorMessage}`);
      throw error instanceof AppError ? error : new AppError(`Failed to get product: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Create a new product
   */
  async createProduct(productData: any): Promise<any> {
    // For AliExpress adapter, we'd typically not create products,
    // but rather import them from AliExpress
    throw new AppError('Direct product creation not supported in AliExpress adapter', 400);
  }
  
  /**
   * Update a product
   * For AliExpress products, we can only update certain metadata locally
   * as most product data is controlled by the AliExpress platform
   */
  async updateProduct(id: string, productData: any): Promise<any> {
    try {
      // Get the product from our database
      const product = await Product.findById(id);
      
      if (!product) {
        throw new AppError(`No product found with id: ${id}`, 404);
      }
      
      if (product.provider !== 'aliexpress') {
        throw new AppError('This product is not from AliExpress', 400);
      }
      
      // For AliExpress products, we can only update certain metadata fields locally
      // as the core product data is controlled by AliExpress
      const allowedUpdates = ['category', 'subcategory', 'tags', 'isActive', 'localPrice', 'localDescription'];
      const updates: Record<string, any> = {};
      
      for (const key of allowedUpdates) {
        if (productData[key] !== undefined) {
          updates[key] = productData[key];
        }
      }
      
      logger.info(`Updating local metadata for AliExpress product: ${id}`, {
        externalId: product.externalId,
        updates: Object.keys(updates)
      });
      
      // If the product has an externalId, we should first check if it still exists on AliExpress
      // and potentially refresh the data to avoid updating a product that's been removed
      if (product.externalId) {
        try {
          // Verify the product still exists on AliExpress
          // Use the store ID from the product's aliExpressSource if available
          const storeId = product.aliExpressSource?.storeId || undefined;
          
          const apiResponse = await this.callAliExpressAPI('ds/product/get', 'POST', {
            product_id: product.externalId
          }, storeId);
          
          if (!apiResponse || !apiResponse.data) {
            logger.warn(`AliExpress product ${product.externalId} might no longer exist, proceeding with caution`);
          } else {
            // Product exists, we can proceed with confidence
            logger.info(`Verified AliExpress product ${product.externalId} still exists`);
            
            // Note: Some price/inventory changes could be made through special AliExpress API endpoints
            // if the platform provides them and we have the right permissions, but this is typically
            // not available for third-party integrations
          }
        } catch (apiError) {
          // Log the error but continue with the local update
          const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown error';
          logger.warn(`Failed to verify AliExpress product ${product.externalId}: ${errorMessage}`);
        }
      }
      
      // Update the local database record
      const updatedProduct = await Product.findByIdAndUpdate(
        id,
        updates,
        { new: true, runValidators: true }
      );
      
      // Handle potential null case
      if (!updatedProduct) {
        throw new AppError(`Failed to update product with id: ${id}`, 500);
      }
      
      // We'll also log that this is a local-only update
      logger.info(`Updated local metadata for AliExpress product: ${id}`, {
        externalId: product.externalId,
        updatedFields: Object.keys(updates)
      });
      
      return {
        ...updatedProduct.toObject(),
        message: 'Only local metadata was updated. Core product data is managed on AliExpress.'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error updating AliExpress product: ${errorMessage}`);
      throw new AppError(`Failed to update product: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Delete a product
   * For AliExpress products, we mark them as inactive in our local database
   * since products cannot be deleted directly through the AliExpress API
   */
  async deleteProduct(id: string): Promise<any> {
    try {
      // Get the product from our database
      const product = await Product.findById(id);
      
      if (!product) {
        throw new AppError(`No product found with id: ${id}`, 404);
      }
      
      if (product.provider !== 'aliexpress') {
        throw new AppError('This product is not from AliExpress', 400);
      }

      logger.info(`Processing delete request for AliExpress product: ${id}`, {
        externalId: product.externalId
      });
      
      // If the product has an externalId, we should first check if it still exists on AliExpress
      // This helps us maintain data consistency
      if (product.externalId) {
        try {
          // Verify the product still exists on AliExpress
          // Use the store ID from the product's aliExpressSource if available
          const storeId = product.aliExpressSource?.storeId || undefined;
          
          const apiResponse = await this.callAliExpressAPI('ds/product/get', 'POST', {
            product_id: product.externalId
          }, storeId);
          
          if (!apiResponse || !apiResponse.data) {
            // Product might no longer exist on AliExpress, so it's valid to remove it locally
            logger.warn(`AliExpress product ${product.externalId} not found on AliExpress, proceeding with local removal`);
          } else {
            // Product exists on AliExpress, so we're only marking it as inactive locally
            logger.info(`AliExpress product ${product.externalId} still exists on platform, marking as inactive locally only`);
          }
        } catch (apiError) {
          // Log the error but continue with the local update
          const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown error';
          logger.warn(`Failed to verify AliExpress product ${product.externalId}: ${errorMessage}`);
        }
      }
      
      // For AliExpress products, we mark as inactive instead of deleting
      // This ensures we don't lose the record of products we've imported
      product.isActive = false;
      
      // Add metadata about deletion if schema supports it
      // Using set() to avoid TypeScript errors if deletedAt isn't in the schema
      if (typeof product.set === 'function') {
        product.set('metadata.deletedAt', new Date());
        product.set('metadata.deletionReason', 'User requested removal from catalog');
      }
      
      await product.save();
      
      logger.info(`AliExpress product marked as inactive: ${id}`);
      
      return { 
        message: 'AliExpress product marked as inactive', 
        note: 'The product remains available on AliExpress but will no longer appear in your local catalog.'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error deleting AliExpress product: ${errorMessage}`);
      throw new AppError(`Failed to delete product: ${errorMessage}`, 500);
    }
  }
  
  // searchProducts implementation moved to unified API version above
  
  /**
   * Get product inventory directly from AliExpress API
   */
  async getProductInventory(id: string): Promise<any> {
    try {
      const product = await this.getProductById(id);
      
      if (product.provider !== 'aliexpress') {
        throw new AppError('This product is not from AliExpress', 400);
      }
      
      if (!product.externalId) {
        throw new AppError('Product missing external ID required for inventory check', 400);
      }
      
      logger.info(`Fetching real-time inventory for AliExpress product: ${product.externalId}`);
      
      // Call AliExpress API to get current inventory information
      // Using the product detail endpoint which includes stock information
      // Use the storeId from the product's aliExpressSource if available
      const storeId = product.aliExpressSource?.storeId || undefined;
      
      const response = await this.callAliExpressAPI('ds/product/get', 'POST', {
        product_id: product.externalId
      }, storeId);
      
      if (!response || !response.data) {
        logger.warn(`Invalid response from AliExpress API for product inventory check: ${product.externalId}`);
        // Fall back to local data if the API fails
        return {
          id: product._id,
          sku: product.sku,
          stock: product.stock,
          variants: product.variants?.map((variant: any) => ({
            sku: variant.sku,
            stock: variant.stock
          })) || [],
          source: 'local_database',
          lastUpdated: product.updatedAt || new Date(),
          message: 'Using local inventory data (failed to fetch from AliExpress API)'
        };
      }
      
      // Extract inventory information from the API response
      const productData = response.data;
      const currentStock = productData.stock || productData.available_quantity || 0;
      
      // Define proper interface for variant type
      interface VariantInfo {
        sku: string;
        stock: number;
        properties: Record<string, any>;
      }
      
      // Extract variant inventory if available with explicit typing
      const variants: VariantInfo[] = [];
      if (productData.variants && Array.isArray(productData.variants)) {
        productData.variants.forEach((variant: any) => {
          variants.push({
            sku: variant.sku || `${product.sku}-${variant.id || Math.random().toString(36).substring(2, 10)}`,
            stock: variant.stock || variant.available_quantity || 0,
            properties: variant.properties || {}
          });
        });
      }
      
      // Update local record with the latest stock information
      product.stock = currentStock;
      if (variants.length > 0) {
        product.variants = variants;
      }
      
      // Save the updated inventory information to our database
      await product.save();
      
      logger.info(`Successfully fetched inventory for AliExpress product: ${product.externalId}`, {
        stock: currentStock,
        variantCount: variants.length
      });
      
      return {
        id: product._id,
        sku: product.sku,
        stock: currentStock,
        variants: variants,
        source: 'aliexpress_api',
        lastUpdated: new Date(),
        message: 'Real-time inventory data from AliExpress API'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error fetching inventory for AliExpress product: ${errorMessage}`);
      throw new AppError(`Failed to fetch product inventory: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Update product inventory
   * For AliExpress products, we need to sync with AliExpress API
   */
  async updateProductInventory(id: string, quantity: number): Promise<any> {
    try {
      // Get the product from our database
      const product = await Product.findById(id);
      
      if (!product) {
        throw new AppError(`No product found with id: ${id}`, 404);
      }
      
      if (product.provider !== 'aliexpress' || !product.externalId) {
        throw new AppError('This product is not from AliExpress or missing externalId', 400);
      }
      
      logger.info(`Updating inventory for AliExpress product ${product.externalId} to ${quantity}`);
      
      // First, update our local record
      product.stock = quantity;
      await product.save();
      
      // The actual inventory is managed on AliExpress side, so we're just updating our local copy
      // However, we could call an AliExpress API endpoint here if they provide inventory management
      // capabilities through their API in the future
      
      return {
        id: product._id,
        sku: product.sku,
        stock: product.stock,
        message: 'Inventory updated in local database'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error updating inventory for AliExpress product: ${errorMessage}`);
      throw new AppError(`Failed to update inventory: ${errorMessage}`, 500);
    }
  }

  /**
   * Import products from AliExpress stores
   */
  async importProducts(): Promise<{
    message: string;
    imported: number;
    updated: number;
    total: number;
  }> {
    try {
      let imported = 0;
      let updated = 0;
      const { stores } = config.aliexpress;
      
      // Fetch products from each configured store
      for (const storeId of stores.ids) {
        logger.info(`Fetching products from AliExpress store: ${storeId}`);
        
        // Call the AliExpress Open API to get store products
        // Using 'aliexpress.ds.product.list.get' endpoint based on docs
        const storeProducts = await this.callAliExpressAPI('ds/product/list', 'GET', {
          store_id: storeId,
          page_size: stores.defaultPageSize,
          current_page: 1,
          sort_by: 'created_at',
          sort_by_direction: 'desc'
        });
        
        // Check if we received valid products
        const products = Array.isArray(storeProducts) ? storeProducts : 
                        (storeProducts.products || storeProducts.product_list || []);
        
        if (!Array.isArray(products) || products.length === 0) {
          logger.warn(`No products found for store ${storeId}, or unexpected response format`);
          continue;
        }
        
        logger.info(`Found ${products.length} products from store ${storeId}`);
        
        // Process each product
        for (const aliProduct of products) {
          // Map to our product model
          const productData = this.mapToProductModel(aliProduct);
          
          // Check if product already exists
          const externalId = aliProduct.product_id || aliProduct.external_id;
          if (!externalId) {
            logger.warn('Skipping product without ID', aliProduct);
            continue;
          }
          
          const existingProduct = await Product.findOne({ 
            provider: 'aliexpress',
            externalId: externalId.toString()
          });
          
          if (existingProduct) {
            // Update existing product
            await Product.updateOne(
              { _id: existingProduct._id },
              productData
            );
            updated++;

            // Sync updated product to Stripe
            try {
              const updatedProduct = await Product.findById(existingProduct._id);
              if (updatedProduct) {
                await stripeProductSyncService.syncProductToStripe(updatedProduct);
                logger.info(`Synced updated product to Stripe: ${updatedProduct.name}`);
              }
            } catch (stripeError) {
              logger.error(`Failed to sync updated product to Stripe: ${existingProduct.name}`, stripeError);
            }
          } else {
            // Create new product
            const newProduct = await Product.create(productData);
            imported++;

            // Sync new product to Stripe
            try {
              await stripeProductSyncService.syncProductToStripe(newProduct);
              logger.info(`Synced new product to Stripe: ${newProduct.name}`);
            } catch (stripeError) {
              logger.error(`Failed to sync new product to Stripe: ${newProduct.name}`, stripeError);
            }
          }
        }
      }
      
      const total = imported + updated;
      return {
        message: `Successfully processed ${total} products (${imported} imported, ${updated} updated)`,
        imported,
        updated,
        total
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error importing products from AliExpress: ${errorMessage}`);
      throw new AppError(`Failed to import products: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Synchronize products with AliExpress platform
   * This method handles both fetching new products and updating existing ones
   * @param options Optional parameters for sync process
   */
  async syncProducts(options?: {
    storeId?: string;
    forceRefresh?: boolean;
    limit?: number;
    lastSyncTime?: Date;
    categories?: string[];
  }): Promise<{
    success: boolean;
    message: string;
    stats: {
      total: number;
      added: number;
      updated: number;
      removed: number;
      errors: number;
    };
  }> {
    try {
      logger.info('Starting product synchronization with AliExpress');
      
      // Initialize counters for sync stats
      const stats = {
        total: 0,
        added: 0,
        updated: 0,
        removed: 0,
        errors: 0
      };
      
      // Get store IDs to sync - either the one specified or all configured stores
      const storesToSync = options?.storeId ? 
        [options.storeId] : 
        config.aliexpress?.stores?.ids || [];
      
      if (storesToSync.length === 0) {
        throw new AppError('No AliExpress stores configured for synchronization', 400);
      }
      
      // Determine the time threshold for updates
      const lastSyncTime = options?.lastSyncTime || new Date(Date.now() - 24 * 60 * 60 * 1000); // Default to 24 hours ago
      const syncStartTime = new Date();
      
      // Set the limit for how many products to process per store
      const limit = options?.limit || config.aliexpress?.stores?.defaultPageSize || 50;
      
      // Get category filter if specified
      const categoryFilter = options?.categories && options.categories.length > 0 ?
        { categories: { $in: options.categories } } : {};
      
      // Process each store
      for (const storeId of storesToSync) {
        logger.info(`Synchronizing products from AliExpress store: ${storeId}`);
        
        // Call the AliExpress Open API to get store products
        // Using pagination to handle potentially large catalogs
        let currentPage = 1;
        let hasMorePages = true;
        
        while (hasMorePages) {
          try {
            // Fetch products from AliExpress API
            const apiParams: Record<string, any> = {
              store_id: storeId,
              page_size: limit,
              current_page: currentPage,
              sort_by: options?.forceRefresh ? 'created_at' : 'updated_at',
              sort_by_direction: 'desc'
            };
            
            // Apply category filtering if specified
            if (options?.categories && options.categories.length > 0) {
              apiParams.category_ids = options.categories.join(',');
            }
            
            // If not doing a force refresh, only get products updated since the last sync
            if (!options?.forceRefresh && lastSyncTime) {
              apiParams.updated_at_min = lastSyncTime.toISOString();
            }
            
            const storeProducts = await this.callAliExpressAPI('ds/product/list', 'GET', apiParams);
            
            // Check if we received valid products
            const products = Array.isArray(storeProducts) ? storeProducts : 
                           (storeProducts.products || storeProducts.product_list || []);
            
            if (!Array.isArray(products) || products.length === 0) {
              logger.info(`No${currentPage > 1 ? ' more' : ''} products found for store ${storeId}`);
              hasMorePages = false;
              continue;
            }
            
            logger.info(`Processing ${products.length} products from store ${storeId} (page ${currentPage})`);
            stats.total += products.length;
            
            // Track external IDs to identify removed products later
            const currentProductIds: string[] = [];
            
            // Process each product
            for (const aliProduct of products) {
              try {
                // Map to our product model
                const productData = this.mapToProductModel(aliProduct);
                
                // Check if product already exists
                const externalId = aliProduct.product_id || aliProduct.external_id;
                if (!externalId) {
                  logger.warn('Skipping product without ID', aliProduct);
                  continue;
                }
                
                currentProductIds.push(externalId.toString());
                
                const existingProduct = await Product.findOne({ 
                  provider: 'aliexpress',
                  externalId: externalId.toString()
                });
                
                if (existingProduct) {
                  // Update existing product
                  await Product.updateOne(
                    { _id: existingProduct._id },
                    {
                      ...productData,
                      lastSynced: new Date(),
                      // Preserve local customizations
                      $setOnInsert: {
                        category: existingProduct.category,
                        subcategory: existingProduct.subcategory,
                        tags: existingProduct.tags
                      }
                    },
                    { upsert: true }
                  );
                  stats.updated++;

                  // Sync updated product to Stripe
                  try {
                    const updatedProduct = await Product.findById(existingProduct._id);
                    if (updatedProduct) {
                      await stripeProductSyncService.syncProductToStripe(updatedProduct);
                      logger.info(`Synced updated product to Stripe: ${updatedProduct.name}`);
                    }
                  } catch (stripeError) {
                    logger.error(`Failed to sync updated product to Stripe: ${existingProduct.name}`, stripeError);
                  }
                } else {
                  // Create new product
                  const newProduct = await Product.create({
                    ...productData,
                    lastSynced: new Date()
                  });
                  stats.added++;

                  // Sync new product to Stripe
                  try {
                    await stripeProductSyncService.syncProductToStripe(newProduct);
                    logger.info(`Synced new product to Stripe: ${newProduct.name}`);
                  } catch (stripeError) {
                    logger.error(`Failed to sync new product to Stripe: ${newProduct.name}`, stripeError);
                  }
                }
              } catch (productError) {
                const errorMessage = productError instanceof Error ? productError.message : 'Unknown error';
                logger.error(`Error processing AliExpress product: ${errorMessage}`);
                stats.errors++;
              }
            }
            
            // Check if we should continue to next page
            if (products.length < limit) {
              hasMorePages = false;
            } else {
              currentPage++;
            }
            
            // If we're doing a full refresh, identify and handle removed products
            if (options?.forceRefresh && currentPage === 2 && storeId === storesToSync[0]) {
              await this.handleRemovedProducts(currentProductIds, storeId, stats);
            }
            
          } catch (pageError) {
            const errorMessage = pageError instanceof Error ? pageError.message : 'Unknown error';
            logger.error(`Error fetching page ${currentPage} from AliExpress: ${errorMessage}`);
            stats.errors++;
            hasMorePages = false;
          }
        }
      }
      
      // Update last sync timestamp in configuration or database
      // This could be stored in a settings collection or similar
      try {
        // Example: await Settings.findOneAndUpdate({ key: 'aliexpress_last_sync' }, { value: syncStartTime }, { upsert: true });
        logger.info(`Sync completed successfully at ${syncStartTime.toISOString()}`);
      } catch (settingsError) {
        logger.warn('Failed to update last sync timestamp', settingsError);
      }
      
      return {
        success: stats.errors === 0,
        message: `Successfully synchronized ${stats.total} products (${stats.added} added, ${stats.updated} updated, ${stats.removed} removed, ${stats.errors} errors)`,
        stats
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error synchronizing products with AliExpress: ${errorMessage}`);
      throw new AppError(`Failed to synchronize products: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Helper method to handle removed products during sync
   */
  private async handleRemovedProducts(currentProductIds: string[], storeId: string, stats: any): Promise<void> {
    try {
      // Find products that exist in our database but not in the current API response
      const removedProducts = await Product.find({
        provider: 'aliexpress',
        externalId: { $nin: currentProductIds },
        storeId: storeId,
        isActive: true // Only consider active products
      });
      
      if (removedProducts.length > 0) {
        logger.info(`Found ${removedProducts.length} products that no longer exist in AliExpress store ${storeId}`);
        
        // Mark products as inactive rather than delete them
        for (const product of removedProducts) {
          try {
            product.isActive = false;
            product.set('metadata.removedAt', new Date());
            product.set('metadata.removalReason', 'No longer available on AliExpress');
            await product.save();
            stats.removed++;
          } catch (productError) {
            logger.error(`Error marking product ${product._id} as removed: ${productError}`);
            stats.errors++;
          }
        }
      }
    } catch (error) {
      logger.error('Error handling removed products:', error);
      throw error;
    }
  }
  
  /**
   * Perform bulk operations
   */
  async bulkOperations(operations: Array<{
    type: 'create' | 'update' | 'delete';
    id?: string;
    data?: any;
  }>): Promise<{
    success: Array<{
      type: string;
      id: string;
      data?: any;
    }>;
    errors: Array<{
      type: string;
      id?: string;
      error: string;
    }>;
  }> {
    // For AliExpress adapter, we only allow limited operations
    const success: Array<{
      type: string;
      id: string;
      data?: any;
    }> = [];
    
    const errors: Array<{
      type: string;
      id?: string;
      error: string;
    }> = [];
    
    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'update':
            if (!operation.id) {
              throw new Error('ID is required for update operations');
            }
            
            // Only allow updating certain fields for AliExpress products
            const product = await Product.findById(operation.id);
            
            if (!product || product.provider !== 'aliexpress') {
              throw new Error('Product not found or not an AliExpress product');
            }
            
            // Only allow updating certain fields
            const allowedUpdates = ['category', 'subcategory', 'tags', 'isActive'];
            const updates: Record<string, any> = {};
            
            for (const key of allowedUpdates) {
              if (operation.data && operation.data[key] !== undefined) {
                updates[key] = operation.data[key];
              }
            }
            
            const updatedProduct = await Product.findByIdAndUpdate(
              operation.id,
              updates,
              { new: true }
            );
            
            success.push({
              type: 'update',
              id: operation.id,
              data: { updated: Object.keys(updates) }
            });
            break;
            
          case 'delete':
            // For AliExpress products, we only mark as inactive
            if (!operation.id) {
              throw new Error('ID is required for delete operations');
            }
            
            const productToDelete = await Product.findById(operation.id);
            
            if (!productToDelete || productToDelete.provider !== 'aliexpress') {
              throw new Error('Product not found or not an AliExpress product');
            }
            
            // Mark as inactive instead of deleting
            await Product.findByIdAndUpdate(
              operation.id,
              { isActive: false },
              { new: true }
            );
            
            success.push({
              type: 'delete',
              id: operation.id,
              data: { status: 'marked-inactive' }
            });
            break;
            
          case 'create':
            // AliExpress adapter doesn't support direct creation
            errors.push({
              type: 'create',
              error: 'Direct product creation not supported in AliExpress adapter'
            });
            break;
            
          default:
            errors.push({
              type: operation.type,
              id: operation.id,
              error: 'Unsupported operation type'
            });
        }
      } catch (error: any) {
        errors.push({
          type: operation.type,
          id: operation.id,
          error: error.message || 'Unknown error'
        });
      }
    }
    
    return { success, errors };
  }
  
  // NOTE: A complete syncProducts method has been implemented above

  /**
   * Get products by category from AliExpress API
   * @param categoryId Category ID
   * @param options Options for including subcategories and filtering
   */
  async getProductsByCategory(categoryId: string, options: {
    includeSubcategories?: boolean;
    filters?: Record<string, any>;
    sort?: string;
    page?: number;
    limit?: number;
    fields?: string;
  } = {}): Promise<{
    products: any[];
    totalCount: number;
    page: number;
    limit: number;
    totalPages: number;
    category: any;
  }> {
  // The actual method implementation continues from here:
    try {
      // Log the request with structured logging
      logger.info('Fetching products by category from AliExpress API', {
        categoryId,
        options
      });
      
      // Set up query parameters for the API call
      const page = options?.page || 1;
      const limit = options?.limit || 20;
      const sortBy = options?.sort || 'default';
      const sortOrder = sortBy?.startsWith('-') ? 'desc' : 'asc';
      const sortField = sortBy?.startsWith('-') ? sortBy.substring(1) : sortBy;
      
      // Use the ds/product/list API with category filter
      // The AliExpress API documentation indicates we can filter by category_id 
      const apiParams = {
        page_no: page,
        page_size: limit,
        sort: sortField,
        sort_direction: sortOrder,
        category_id: categoryId,
        // Include store IDs from config to only get products from our stores
        store_ids: this.storeIds?.join(',') || ''
      };
      
      // Call the AliExpress API with the correct parameter order (endpoint, method, params)
      const response = await this.callAliExpressAPI('ds/product/list', 'POST', apiParams);
      
      // Try to find the category (optional, but needed for interface compliance)
      let category;
      try {
        category = await Category.findById(categoryId);
      } catch (categoryError) {
        logger.warn('Could not fetch category details', { categoryId });
        // We'll continue even if category is not found as we still have the API results
        category = { _id: categoryId, name: 'Unknown Category' };
      }
      
      // Handle empty or invalid response
      if (!response || !response.data || !Array.isArray(response.data.products)) {
        logger.warn('AliExpress API returned invalid data structure for products by category', { 
          categoryId, 
          responseStructure: response ? Object.keys(response) : null
        });
        
        // Return empty results in the format required by the interface
        return {
          products: [],
          totalCount: 0,
          page,
          limit,
          totalPages: 0,
          category
        };
      }
      
      // Map the AliExpress API response to our product model
      const products = await Promise.all(
        response.data.products.map(async (productData: any) => {
          return await this.mapToProductModel(productData);
        })
      );
      
      // Get total count and calculate total pages
      const totalCount = response.data.total_count || products.length;
      const totalPages = Math.ceil(totalCount / limit);
      
      // Log success with structured data
      logger.info('Successfully fetched products by category from AliExpress API', { 
        categoryId, 
        resultCount: products.length,
        totalCount,
        totalPages
      });
      
      // Return in the format required by the interface
      return {
        products,
        totalCount,
        page,
        limit,
        totalPages,
        category
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Error fetching products by category from AliExpress API', {
        error: errorMessage,
        categoryId
      });
      throw new AppError(`Failed to get products by category: ${errorMessage}`, 500);
    }
  }
}