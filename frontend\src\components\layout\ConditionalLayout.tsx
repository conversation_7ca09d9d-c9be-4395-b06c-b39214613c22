'use client';

import { usePathname } from 'next/navigation';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import dynamic from 'next/dynamic';

// Import the ChatWidget with dynamic import to avoid SSR issues
const ClientChatWidget = dynamic(
  () => import('@/components/chatbot/ClientChatWidget'),
  { ssr: false }
);

// Import the ComparisonWidget with dynamic import to avoid SSR issues
const ComparisonWidget = dynamic(
  () => import('@/components/comparison/ComparisonWidget'),
  { ssr: false }
);

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Check if we're on an admin page
  const isAdminPage = pathname?.startsWith('/admin');
  
  // For admin pages, render children without header/footer
  if (isAdminPage) {
    return <>{children}</>;
  }
  
  // For regular pages, render with header/footer and widgets
  return (
    <>
      <Header />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
      
      {/* Chat Widget - Available on non-admin pages */}
      <ClientChatWidget
        title="Wig Style Assistant"
        greeting="Hello! I'm your personal wig stylist. How can I help you today?"
        position="right"
        bubbleSize={60}
      />

      {/* Comparison Widget - Available on non-admin pages */}
      <ComparisonWidget />
    </>
  );
}
