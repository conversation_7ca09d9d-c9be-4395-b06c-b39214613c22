'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  ClockIcon,
  BellIcon,
  LinkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  MicrophoneIcon
} from '@heroicons/react/24/outline';

interface WhatsAppConfig {
  enabled: boolean;
  phoneNumber: string;
  businessName: string;
  webhookUrl: string;
  welcomeMessage: string;
  offlineMessage: string;
  businessHours: {
    start: string;
    end: string;
    timezone: string;
    workDays: string;
  };
  autoReply: {
    enabled: boolean;
    delay: number;
    keywords: Array<{
      keyword: string;
      response: string;
    }>;
  };
  voiceNotes: {
    enabled: boolean;
    maxDuration: number;
    transcription: boolean;
    voiceResponse: boolean;
  };
  missedCalls: {
    enabled: boolean;
    message: string;
    delay: number;
  };
}

export default function WhatsAppConfigPage() {
  const [config, setConfig] = useState<WhatsAppConfig>({
    enabled: true,
    phoneNumber: '',
    businessName: 'Pelucas Chic',
    webhookUrl: '',
    welcomeMessage: 'Welcome to Pelucas Chic! 🌟 How can I help you find the perfect wig today?',
    offlineMessage: 'Thank you for your message! We are currently offline but will respond as soon as we\'re back. Business hours: Mon-Fri 9AM-6PM EST.',
    businessHours: {
      start: '09:00',
      end: '18:00',
      timezone: 'America/New_York',
      workDays: 'Mon-Fri'
    },
    autoReply: {
      enabled: true,
      delay: 2,
      keywords: [
        { keyword: 'price', response: 'Our wigs range from $50 to $500. Would you like to see our catalog?' },
        { keyword: 'catalog', response: 'You can view our full catalog at pelucaschic.com/catalog' },
        { keyword: 'shipping', response: 'We offer free shipping on orders over $100! Delivery takes 5-7 business days.' }
      ]
    },
    voiceNotes: {
      enabled: true,
      maxDuration: 300,
      transcription: true,
      voiceResponse: true
    },
    missedCalls: {
      enabled: true,
      message: 'Hi! I noticed you tried to call. Please send me a message and I\'ll help you find the perfect wig! 💕',
      delay: 5
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  const workDayOptions = [
    'Mon-Fri',
    'Mon-Sat',
    'Mon-Sun',
    'Tue-Sat',
    'Wed-Sun'
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/admin/config/whatsapp', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching WhatsApp config:', error);
      toast.error('Failed to load WhatsApp configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch('/api/admin/config/whatsapp', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('WhatsApp configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating WhatsApp config:', error);
      toast.error('Failed to update WhatsApp configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch('/api/admin/api-keys/whatsapp/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error testing WhatsApp connection:', error);
      toast.error('Failed to test WhatsApp connection');
    } finally {
      setTesting(false);
    }
  };

  const addKeyword = () => {
    setConfig(prev => ({
      ...prev,
      autoReply: {
        ...prev.autoReply,
        keywords: [
          ...prev.autoReply.keywords,
          { keyword: '', response: '' }
        ]
      }
    }));
  };

  const updateKeyword = (index: number, field: 'keyword' | 'response', value: string) => {
    setConfig(prev => ({
      ...prev,
      autoReply: {
        ...prev.autoReply,
        keywords: prev.autoReply.keywords.map((k, i) => 
          i === index ? { ...k, [field]: value } : k
        )
      }
    }));
  };

  const removeKeyword = (index: number) => {
    setConfig(prev => ({
      ...prev,
      autoReply: {
        ...prev.autoReply,
        keywords: prev.autoReply.keywords.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">WhatsApp Business Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure your WhatsApp Business API integration and automated responses
          </p>
        </div>
        <button
          onClick={handleTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {testing ? (
            <>
              <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
              Testing...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Test Connection
            </>
          )}
        </button>
      </div>

      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2" />
            Basic Settings
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable WhatsApp Integration</h3>
              <p className="text-sm text-gray-500">
                Turn on/off WhatsApp Business API integration
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <PhoneIcon className="inline h-4 w-4 mr-1" />
                WhatsApp Business Phone Number
              </label>
              <input
                type="tel"
                value={config.phoneNumber}
                onChange={(e) => setConfig(prev => ({ ...prev, phoneNumber: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="+1234567890"
              />
              <p className="mt-1 text-sm text-gray-500">
                Include country code (e.g., +1 for US)
              </p>
            </div>

            {/* Business Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Name
              </label>
              <input
                type="text"
                value={config.businessName}
                onChange={(e) => setConfig(prev => ({ ...prev, businessName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Your Business Name"
              />
            </div>
          </div>

          {/* Webhook URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <LinkIcon className="inline h-4 w-4 mr-1" />
              Webhook URL
            </label>
            <input
              type="url"
              value={config.webhookUrl}
              onChange={(e) => setConfig(prev => ({ ...prev, webhookUrl: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              placeholder="https://yourdomain.com/api/webhooks/whatsapp"
            />
            <p className="mt-1 text-sm text-gray-500">
              This URL will receive incoming WhatsApp messages and events
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <BellIcon className="h-5 w-5 mr-2" />
            Automated Messages
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Welcome Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Welcome Message
            </label>
            <textarea
              value={config.welcomeMessage}
              onChange={(e) => setConfig(prev => ({ ...prev, welcomeMessage: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              placeholder="Welcome message for new conversations..."
            />
            <p className="mt-1 text-sm text-gray-500">
              Sent when a customer starts a new conversation
            </p>
          </div>

          {/* Offline Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Offline Message
            </label>
            <textarea
              value={config.offlineMessage}
              onChange={(e) => setConfig(prev => ({ ...prev, offlineMessage: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              placeholder="Message sent outside business hours..."
            />
          </div>
        </div>
      </div>

      {/* Business Hours */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <ClockIcon className="h-5 w-5 mr-2" />
            Business Hours
          </h2>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time
              </label>
              <input
                type="time"
                value={config.businessHours.start}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  businessHours: { ...prev.businessHours, start: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Time
              </label>
              <input
                type="time"
                value={config.businessHours.end}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  businessHours: { ...prev.businessHours, end: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={config.businessHours.timezone}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  businessHours: { ...prev.businessHours, timezone: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Work Days
              </label>
              <select
                value={config.businessHours.workDays}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  businessHours: { ...prev.businessHours, workDays: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                {workDayOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Auto-Reply Keywords */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Auto-Reply Keywords
            </h2>
            <button
              onClick={addKeyword}
              className="text-sm text-primary hover:text-primary/80"
            >
              + Add Keyword
            </button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          {config.autoReply.keywords.map((keyword, index) => (
            <div key={index} className="flex gap-4">
              <input
                type="text"
                value={keyword.keyword}
                onChange={(e) => updateKeyword(index, 'keyword', e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Keyword (e.g., price)"
              />
              <input
                type="text"
                value={keyword.response}
                onChange={(e) => updateKeyword(index, 'response', e.target.value)}
                className="flex-2 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Auto-reply message"
              />
              <button
                onClick={() => removeKeyword(index)}
                className="text-red-600 hover:text-red-800"
              >
                Remove
              </button>
            </div>
          ))}
          
          {config.autoReply.keywords.length === 0 && (
            <p className="text-sm text-gray-500 text-center py-4">
              No auto-reply keywords configured. Click "Add Keyword" to create one.
            </p>
          )}
        </div>
      </div>

      {/* Voice Notes */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <MicrophoneIcon className="h-5 w-5 mr-2" />
            Voice Note Settings
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Enable Voice Notes</h3>
                <p className="text-sm text-gray-500">Process incoming voice messages</p>
              </div>
              <button
                onClick={() => setConfig(prev => ({
                  ...prev,
                  voiceNotes: { ...prev.voiceNotes, enabled: !prev.voiceNotes.enabled }
                }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  config.voiceNotes.enabled ? 'bg-green-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config.voiceNotes.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">Voice Responses</h3>
                <p className="text-sm text-gray-500">Send voice replies to customers</p>
              </div>
              <button
                onClick={() => setConfig(prev => ({
                  ...prev,
                  voiceNotes: { ...prev.voiceNotes, voiceResponse: !prev.voiceNotes.voiceResponse }
                }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  config.voiceNotes.voiceResponse ? 'bg-green-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config.voiceNotes.voiceResponse ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Voice Note Duration (seconds)
            </label>
            <input
              type="number"
              value={config.voiceNotes.maxDuration}
              onChange={(e) => setConfig(prev => ({
                ...prev,
                voiceNotes: { ...prev.voiceNotes, maxDuration: parseInt(e.target.value) }
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              min="30"
              max="600"
            />
            <p className="mt-1 text-sm text-gray-500">
              Maximum duration for voice notes (30-600 seconds)
            </p>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>

      {/* Help Box */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-green-700">
            <p className="font-semibold mb-1">WhatsApp Business Features:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>Automated welcome messages for new customers</li>
              <li>Business hours detection with custom offline messages</li>
              <li>Keyword-based auto-replies for common questions</li>
              <li>Voice note transcription and voice responses</li>
              <li>Missed call detection and follow-up messages</li>
              <li>Integration with the AI chatbot for intelligent responses</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 