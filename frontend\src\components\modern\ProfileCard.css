/* ProfileCard CSS */
.pc-card-wrapper {
  --card-opacity: 0.8;
  --pointer-x: 50%;
  --pointer-y: 50%;
  --background-x: 50%;
  --background-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0;
  --pointer-from-left: 0;
  --rotate-x: 0deg;
  --rotate-y: 0deg;

  position: relative;
  width: 100%;
  height: 400px;
  perspective: 1000px;
  transform-style: preserve-3d;
}

.pc-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  background: var(--behind-gradient);
  transform: rotateX(var(--rotate-x)) rotateY(var(--rotate-y));
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.2);
}

.pc-card.active {
  transition: none;
}

.pc-inside {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
  background: var(--inner-gradient);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pc-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at var(--pointer-x) var(--pointer-y),
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 20%,
    transparent 50%
  );
  opacity: var(--pointer-from-center);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pc-glare {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    transparent 30%,
    transparent 70%,
    rgba(255, 255, 255, 0.2) 100%
  );
  opacity: calc(var(--pointer-from-center) * 0.5);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pc-content {
  position: relative;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.pc-avatar-content {
  justify-content: center;
  align-items: center;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.pc-card:hover .avatar {
  transform: scale(1.05);
}

.pc-details h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.pc-details p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.pc-user-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pc-user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pc-mini-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.pc-mini-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pc-user-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pc-handle {
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.pc-status {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.pc-contact-btn {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

.pc-contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4);
}

.pc-contact-btn:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pc-card-wrapper {
    height: 350px;
  }

  .avatar {
    width: 100px;
    height: 100px;
  }

  .pc-details h3 {
    font-size: 1.25rem;
  }

  .pc-details p {
    font-size: 0.875rem;
  }

  .pc-content {
    padding: 20px;
  }

  .pc-user-info {
    padding: 12px;
  }
}

/* Animation enhancements */
.pc-card-wrapper.active .pc-card {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 5px 15px rgba(0, 0, 0, 0.25);
}

.pc-card-wrapper:not(.active) .pc-card {
  transition:
    transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s ease;
}

/* Grain texture overlay */
.pc-inside::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--grain);
  opacity: 0.1;
  mix-blend-mode: overlay;
  pointer-events: none;
  z-index: 1;
}

/* Icon overlay */
.pc-inside::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  background-image: var(--icon);
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.6;
  pointer-events: none;
  z-index: 3;
}
