'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  EnvelopeIcon,
  ServerIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  PaperAirplaneIcon
} from '@heroicons/react/24/outline';

interface EmailConfig {
  enabled: boolean;
  provider: string;
  smtp: {
    host: string;
    port: number;
    secure: boolean;
    username: string;
    password: string;
  };
  from: {
    name: string;
    email: string;
  };
  templates: {
    orderConfirmation: {
      enabled: boolean;
      subject: string;
      template: string;
    };
    shipping: {
      enabled: boolean;
      subject: string;
      template: string;
    };
    welcome: {
      enabled: boolean;
      subject: string;
      template: string;
    };
    newsletter: {
      enabled: boolean;
      subject: string;
      template: string;
    };
  };
  automation: {
    abandonedCart: {
      enabled: boolean;
      delay: number;
      subject: string;
    };
    followUp: {
      enabled: boolean;
      delay: number;
      subject: string;
    };
  };
}

export default function EmailConfigPage() {
  const [config, setConfig] = useState<EmailConfig>({
    enabled: true,
    provider: 'smtp',
    smtp: {
      host: '',
      port: 587,
      secure: false,
      username: '',
      password: ''
    },
    from: {
      name: 'Pelucas Chic',
      email: '<EMAIL>'
    },
    templates: {
      orderConfirmation: {
        enabled: true,
        subject: 'Order Confirmation - Thank you for your purchase!',
        template: 'order_confirmation'
      },
      shipping: {
        enabled: true,
        subject: 'Your order is on its way!',
        template: 'shipping_notification'
      },
      welcome: {
        enabled: true,
        subject: 'Welcome to Pelucas Chic! 💕',
        template: 'welcome'
      },
      newsletter: {
        enabled: true,
        subject: 'Latest Wigs & Beauty Tips',
        template: 'newsletter'
      }
    },
    automation: {
      abandonedCart: {
        enabled: true,
        delay: 24,
        subject: 'Don\'t forget your beautiful wig!'
      },
      followUp: {
        enabled: true,
        delay: 7,
        subject: 'How are you loving your new wig?'
      }
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  const providerOptions = [
    { value: 'smtp', label: 'SMTP Server' },
    { value: 'sendgrid', label: 'SendGrid' },
    { value: 'mailgun', label: 'Mailgun' },
    { value: 'ses', label: 'Amazon SES' },
    { value: 'postmark', label: 'Postmark' }
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/email`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching email config:', error);
      toast.error('Failed to load email configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/email`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('Email configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating email config:', error);
      toast.error('Failed to update email configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/email/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          to: config.from.email,
          subject: 'Test Email from Pelucas Chic Admin',
          template: 'test'
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('Test email sent successfully');
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast.error('Failed to send test email');
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure email settings, templates, and automation
          </p>
        </div>
        <button
          onClick={handleTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {testing ? (
            <>
              <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
              Sending...
            </>
          ) : (
            <>
              <PaperAirplaneIcon className="h-4 w-4 mr-2" />
              Send Test Email
            </>
          )}
        </button>
      </div>

      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <EnvelopeIcon className="h-5 w-5 mr-2" />
            Email Provider
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable Email System</h3>
              <p className="text-sm text-gray-500">
                Turn on/off email notifications and automation
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Provider */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Provider
              </label>
              <select
                value={config.provider}
                onChange={(e) => setConfig(prev => ({ ...prev, provider: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              >
                {providerOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* From Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                From Name
              </label>
              <input
                type="text"
                value={config.from.name}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  from: { ...prev.from, name: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Your Business Name"
              />
            </div>

            {/* From Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                From Email
              </label>
              <input
                type="email"
                value={config.from.email}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  from: { ...prev.from, email: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          {/* SMTP Settings */}
          {config.provider === 'smtp' && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <ServerIcon className="h-5 w-5 mr-2" />
                SMTP Settings
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    SMTP Host
                  </label>
                  <input
                    type="text"
                    value={config.smtp.host}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      smtp: { ...prev.smtp, host: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                    placeholder="smtp.gmail.com"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Port
                  </label>
                  <input
                    type="number"
                    value={config.smtp.port}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      smtp: { ...prev.smtp, port: parseInt(e.target.value) }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                    placeholder="587"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Username
                  </label>
                  <input
                    type="text"
                    value={config.smtp.username}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      smtp: { ...prev.smtp, username: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    value={config.smtp.password}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      smtp: { ...prev.smtp, password: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                    placeholder="App password or SMTP password"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
