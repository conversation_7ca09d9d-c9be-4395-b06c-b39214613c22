'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  CameraIcon,
  PhotoIcon,
  HashtagIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  LinkIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface InstagramConfig {
  enabled: boolean;
  account: {
    username: string;
    accessToken: string;
    businessId: string;
  };
  feed: {
    enabled: boolean;
    postsToShow: number;
    refreshInterval: number;
    showCaptions: boolean;
    showHashtags: boolean;
  };
  posting: {
    enabled: boolean;
    autoPost: boolean;
    defaultHashtags: string[];
    postTemplate: string;
  };
  stories: {
    enabled: boolean;
    autoStory: boolean;
    storyTemplate: string;
  };
  shopping: {
    enabled: boolean;
    productTagging: boolean;
    catalogId: string;
  };
}

export default function InstagramConfigPage() {
  const [config, setConfig] = useState<InstagramConfig>({
    enabled: true,
    account: {
      username: '',
      accessToken: '',
      businessId: ''
    },
    feed: {
      enabled: true,
      postsToShow: 12,
      refreshInterval: 60,
      showCaptions: true,
      showHashtags: false
    },
    posting: {
      enabled: false,
      autoPost: false,
      defaultHashtags: ['#pelucaschic', '#wigs', '#humanhair', '#beauty', '#style'],
      postTemplate: 'Check out our latest wig collection! 💕 #pelucaschic #wigs'
    },
    stories: {
      enabled: false,
      autoStory: false,
      storyTemplate: 'New arrivals at Pelucas Chic! 🌟'
    },
    shopping: {
      enabled: false,
      productTagging: false,
      catalogId: ''
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/instagram`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching Instagram config:', error);
      toast.error('Failed to load Instagram configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/config/instagram`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('Instagram configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating Instagram config:', error);
      toast.error('Failed to update Instagram configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/api-keys/instagram/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error testing Instagram:', error);
      toast.error('Failed to test Instagram connection');
    } finally {
      setTesting(false);
    }
  };

  const addHashtag = () => {
    setConfig(prev => ({
      ...prev,
      posting: {
        ...prev.posting,
        defaultHashtags: [...prev.posting.defaultHashtags, '']
      }
    }));
  };

  const updateHashtag = (index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      posting: {
        ...prev.posting,
        defaultHashtags: prev.posting.defaultHashtags.map((h, i) => i === index ? value : h)
      }
    }));
  };

  const removeHashtag = (index: number) => {
    setConfig(prev => ({
      ...prev,
      posting: {
        ...prev.posting,
        defaultHashtags: prev.posting.defaultHashtags.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Instagram Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure Instagram integration, feed display, and posting automation
          </p>
        </div>
        <button
          onClick={handleTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {testing ? (
            <>
              <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
              Testing...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Test Connection
            </>
          )}
        </button>
      </div>

      {/* Account Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <CameraIcon className="h-5 w-5 mr-2" />
            Instagram Account
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable Instagram Integration</h3>
              <p className="text-sm text-gray-500">
                Turn on/off Instagram features and feed display
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Username */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instagram Username
              </label>
              <input
                type="text"
                value={config.account.username}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  account: { ...prev.account, username: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="@pelucaschic"
              />
            </div>

            {/* Access Token */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Access Token
              </label>
              <input
                type="password"
                value={config.account.accessToken}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  account: { ...prev.account, accessToken: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Instagram API access token"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Feed Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <PhotoIcon className="h-5 w-5 mr-2" />
            Instagram Feed Display
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable Feed */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Show Instagram Feed</h3>
              <p className="text-sm text-gray-500">
                Display Instagram posts on your website
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({
                ...prev,
                feed: { ...prev.feed, enabled: !prev.feed.enabled }
              }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.feed.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.feed.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Posts to Show
              </label>
              <input
                type="number"
                value={config.feed.postsToShow}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  feed: { ...prev.feed, postsToShow: parseInt(e.target.value) }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                min="1"
                max="50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <ClockIcon className="inline h-4 w-4 mr-1" />
                Refresh Interval (minutes)
              </label>
              <input
                type="number"
                value={config.feed.refreshInterval}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  feed: { ...prev.feed, refreshInterval: parseInt(e.target.value) }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                min="15"
                max="1440"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Posting Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <HashtagIcon className="h-5 w-5 mr-2" />
            Auto-Posting Settings
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Default Hashtags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Hashtags
            </label>
            <div className="space-y-2">
              {config.posting.defaultHashtags.map((hashtag, index) => (
                <div key={index} className="flex gap-2">
                  <input
                    type="text"
                    value={hashtag}
                    onChange={(e) => updateHashtag(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                    placeholder="#hashtag"
                  />
                  <button
                    onClick={() => removeHashtag(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              ))}
              <button
                onClick={addHashtag}
                className="text-sm text-primary hover:text-primary/80"
              >
                + Add Hashtag
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
