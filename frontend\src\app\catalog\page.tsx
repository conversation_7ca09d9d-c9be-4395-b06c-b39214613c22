'use client';

import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import ProductCard from '@/components/catalog/ProductCard';
import FilterPanel from '@/components/catalog/FilterPanel';
import SortOptions from '@/components/catalog/SortOptions';
import Pagination from '@/components/catalog/Pagination';
import { UnifiedProductService } from '@/services/unifiedProductService';
import { useDebounce } from '@/hooks/useDebounce';
import LoadingSkeleton from '@/components/catalog/LoadingSkeleton';
import EmptyState from '@/components/catalog/EmptyState';
import ErrorState from '@/components/catalog/ErrorState';
import RecentlyViewed from '@/components/catalog/RecentlyViewed';
import Recommendations from '@/components/catalog/Recommendations';
import Image from 'next/image';

const PAGE_SIZE = 12;

// Updated product interface to match unified system
interface StoreProduct {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  rating: number;
  reviews: number;
  sales: number;
  imageUrl: string;
  slug: string;
  category: string;
  subcategory: string;
  storeId: string;
  storeName: string;
  description: string;
  isNew: boolean;
  isFeatured: boolean;
}

// Import or define the SortOptionType to match unified service
type SortOptionType = 'featured' | 'newest' | 'price-asc' | 'price-desc' | 'rating' | 'sales';

function ProductCatalogContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // State for products and loading
  const [products, setProducts] = useState<StoreProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalProducts, setTotalProducts] = useState(0);
  
  // State for filters
  const [filters, setFilters] = useState({
    priceRange: [0, 1000] as [number, number],
    colors: [],
    lengths: [],
    styles: [],
    density: [],
    capConstruction: [],
    material: [],
  });
  
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  
  // State for sorting
  const [sortOption, setSortOption] = useState<SortOptionType>('featured');
  
  // State for mobile filter drawer
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
  
  // Ref for infinite scroll
  const observerRef = useRef<IntersectionObserver | null>(null);
  const lastProductRef = useRef<HTMLDivElement | null>(null);
  
  // Debounce filter changes to prevent excessive API calls
  const debouncedFilters = useDebounce(filters, 500);
  
  // Fetch products when filters, sort, or pagination changes
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('Fetching products with options:', {
          page: currentPage,
          pageSize: PAGE_SIZE,
          sort: sortOption,
          ...debouncedFilters
        });
        
        // Call API with current filters, sort, and pagination
        const response = await UnifiedProductService.getShopProducts({
          page: currentPage,
          limit: PAGE_SIZE,
          sort: sortOption,
          priceRange: debouncedFilters.priceRange ? { 
            min: debouncedFilters.priceRange[0], 
            max: debouncedFilters.priceRange[1] 
          } : undefined,
          category: debouncedFilters.styles?.length > 0 ? debouncedFilters.styles[0] : undefined,
          rating: debouncedFilters.rating ? Number(debouncedFilters.rating) : undefined
        });
        
        console.log('Received products response:', response);
        
        // Handle the unified service response structure
        if (response.products.length === 0) {
          console.log('No products found. Using fallback.');
          setProducts([]);
          setTotalProducts(0);
        } else {
          setProducts(response.products);
          setTotalProducts(response.totalProducts);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to load products. Please try again later.');
        
        // Fallback to empty state on error
        console.log('Error occurred. Setting empty state.');
        setProducts([]);
        setTotalProducts(0);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProducts();
  }, [debouncedFilters, sortOption, currentPage]);
  
  // Handle filter changes
  const handleFilterChange = (filterType: string, value: any) => {
    console.log(`Filter changed: ${filterType} to`, value);
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
    setCurrentPage(1); // Reset to first page when filters change
  };
  
  // Handle sorting changes
  const handleSortChange = (option: SortOptionType) => {
    console.log(`Sort changed to: ${option}`);
    setSortOption(option);
    setCurrentPage(1); // Reset to first page when sort changes
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  // Clear all filters
  const handleClearFilters = () => {
    setFilters({
      priceRange: [0, 1000],
      colors: [],
      lengths: [],
      styles: [],
      density: [],
      capConstruction: [],
      material: [],
    });
    setCurrentPage(1);
  };
  
  // Toggle mobile filter drawer
  const toggleFilterDrawer = () => {
    setIsFilterDrawerOpen(!isFilterDrawerOpen);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Enhanced Header with Gradient Background */}
      <div className="relative bg-gradient-to-r from-pink-500 via-purple-600 to-indigo-600 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
              Premium Wig Collection
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-8">
              Discover our curated selection of high-quality human hair wigs crafted for natural beauty
            </p>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-white/80 text-sm">Premium Wigs</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">10K+</div>
                <div className="text-white/80 text-sm">Happy Customers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">4.9★</div>
                <div className="text-white/80 text-sm">Average Rating</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">24/7</div>
                <div className="text-white/80 text-sm">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="lg:grid lg:grid-cols-4 lg:gap-12">
          {/* Enhanced Filter Panel - Desktop */}
          <div className="hidden lg:block">
            <div className="sticky top-8">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 px-6 py-4">
                  <h2 className="text-lg font-semibold text-white">Filters</h2>
                </div>
                <div className="p-6">
                  <FilterPanel
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    onClearFilters={handleClearFilters}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Products Section */}
          <div className="lg:col-span-3">
            {/* Enhanced Mobile Filter Button & Sort Options */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <button
                  onClick={toggleFilterDrawer}
                  className="lg:hidden flex items-center justify-center px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl shadow-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
                  </svg>
                  Filters & Sort
                </button>

                <div className="flex items-center gap-4">
                  <SortOptions
                    currentSort={sortOption}
                    onSortChange={handleSortChange}
                    totalCount={totalProducts}
                  />
                </div>
              </div>

              {/* Results Count with Enhanced Styling */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    Showing <span className="font-semibold text-gray-900">{products.length}</span> of <span className="font-semibold text-gray-900">{totalProducts}</span> products
                  </p>
                  {products.length > 0 && (
                    <div className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Premium Quality
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Products Grid */}
            {isLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse">
                    <div className="aspect-square bg-gray-200"></div>
                    <div className="p-6 space-y-3">
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="bg-white rounded-2xl shadow-lg border border-red-100 p-12 text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>
                <p className="text-gray-600 mb-6">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium"
                >
                  Try Again
                </button>
              </div>
            ) : products.length === 0 ? (
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-12 text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600 mb-6">Try adjusting your filters or search criteria</p>
                <button
                  onClick={handleClearFilters}
                  className="px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium"
                >
                  Clear Filters
                </button>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                  {products.map((product, index) => (
                    <div
                      key={product.id}
                      ref={index === products.length - 1 ? lastProductRef : null}
                      className="group transform transition-all duration-300 hover:scale-105"
                    >
                      <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100">
                        <ProductCard
                          id={product.id}
                          slug={product.slug}
                          name={product.title}
                          price={product.price}
                          originalPrice={product.originalPrice}
                          category={product.category}
                          imageUrl={product.imageUrl}
                          rating={product.rating}
                          reviewCount={product.reviews}
                          isNew={product.isNew}
                          isFeatured={product.isFeatured}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                {/* Enhanced Pagination */}
                {totalProducts > PAGE_SIZE && (
                  <div className="mt-16 bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={Math.ceil(totalProducts / PAGE_SIZE)}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Enhanced Recently Viewed & Recommendations */}
        <div className="mt-20 space-y-16">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            <RecentlyViewed />
          </div>
          <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl shadow-lg border border-gray-100 p-8">
            <Recommendations />
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Filter Drawer */}
      {isFilterDrawerOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" onClick={toggleFilterDrawer} />
          <div className="fixed right-0 top-0 h-full w-full max-w-sm bg-white shadow-2xl">
            <div className="flex items-center justify-between p-6 bg-gradient-to-r from-pink-500 to-purple-600">
              <h2 className="text-xl font-semibold text-white">Filters & Sort</h2>
              <button
                onClick={toggleFilterDrawer}
                className="p-2 -mr-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto h-full pb-20">
              <FilterPanel
                filters={filters}
                onFilterChange={handleFilterChange}
                onClearFilters={handleClearFilters}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default function ProductCatalog() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading catalog...</p>
        </div>
      </div>
    }>
      <ProductCatalogContent />
    </Suspense>
  );
} 