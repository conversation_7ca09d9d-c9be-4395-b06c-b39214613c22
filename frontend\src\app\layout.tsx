import type { Metadata } from 'next';
import { Inter, Playfair_Display } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import AnalyticsScripts from '@/components/analytics/AnalyticsScripts';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import dynamic from 'next/dynamic';
import ConditionalLayout from '@/components/layout/ConditionalLayout';

// Import the ChatWidget with dynamic import to avoid SSR issues
const ClientChatWidget = dynamic(
  () => import('@/components/chatbot/ClientChatWidget'),
  { ssr: false }
);

// Import the ComparisonWidget with dynamic import to avoid SSR issues
const ComparisonWidget = dynamic(
  () => import('@/components/comparison/ComparisonWidget'),
  { ssr: false }
);

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

const playfair = Playfair_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair',
});

export const metadata: Metadata = {
  title: 'Pelucas Chic - Premium Custom Wigs',
  description: 'Discover premium custom wigs crafted with care for a natural look. Explore our collection of hand-tied, lace front, and full lace wigs.',
  keywords: 'custom wigs, premium wigs, hair wigs, natural wigs, lace front wigs',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <head>
        {/* Basic meta tags only - analytics moved inside Providers */}
      </head>
      <body className="min-h-screen bg-light flex flex-col">
        <ErrorBoundary>
          <Providers>
            {/* Analytics Scripts now have access to SettingsProvider */}
            <AnalyticsScripts />

            <ConditionalLayout>
              {children}
            </ConditionalLayout>
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  );
} 