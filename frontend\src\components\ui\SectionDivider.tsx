'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface SectionDividerProps {
  variant?: 'wave-top' | 'wave-bottom' | 'wave-both' | 'curve-top' | 'curve-bottom';
  gradient?: 'purple-pink' | 'blue-purple' | 'pink-orange' | 'teal-blue' | 'custom';
  customGradient?: string;
  height?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  flip?: boolean;
}

const SectionDivider: React.FC<SectionDividerProps> = ({
  variant = 'wave-top',
  gradient = 'purple-pink',
  customGradient,
  height = 'md',
  className = '',
  flip = false
}) => {
  const getGradient = () => {
    if (customGradient) return customGradient;

    const gradients = {
      'purple-pink': 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      'blue-purple': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #a8edea 100%)',
      'pink-orange': 'linear-gradient(135deg, #fa709a 0%, #fee140 50%, #ffecd2 100%)',
      'teal-blue': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 50%, #667eea 100%)',
      'custom': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    };

    return gradients[gradient];
  };

  const getHeight = () => {
    const heights = {
      sm: '60px',
      md: '100px',
      lg: '150px',
      xl: '200px'
    };
    return heights[height];
  };

  const getWavePath = () => {
    const paths = {
      'wave-top': 'M0,0 C150,100 350,0 500,50 C650,100 850,0 1000,50 C1150,100 1350,0 1500,50 L1500,0 L0,0 Z',
      'wave-bottom': 'M0,100 C150,0 350,100 500,50 C650,0 850,100 1000,50 C1150,0 1350,100 1500,50 L1500,100 L0,100 Z',
      'curve-top': 'M0,0 Q750,100 1500,0 L1500,0 L0,0 Z',
      'curve-bottom': 'M0,100 Q750,0 1500,100 L1500,100 L0,100 Z',
      'wave-both': 'M0,20 C150,80 350,20 500,50 C650,80 850,20 1000,50 C1150,80 1350,20 1500,50 L1500,80 C1350,20 1150,80 1000,50 C850,20 650,80 500,50 C350,20 150,80 0,50 Z'
    };
    return paths[variant];
  };

  return (
    <div
      className={`relative w-full overflow-hidden ${className}`}
      style={{ height: getHeight() }}
    >
      <motion.div
        className="absolute inset-0"
        style={{
          background: getGradient(),
          transform: flip ? 'scaleY(-1)' : 'none'
        }}
        initial={{ opacity: 0, y: flip ? -20 : 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, ease: 'easeOut' }}
      >
        <svg
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 1500 100"
          preserveAspectRatio="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            <linearGradient id={`gradient-${variant}-${gradient}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.1)" />
              <stop offset="50%" stopColor="rgba(255,255,255,0.05)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0)" />
            </linearGradient>
          </defs>
          <path
            d={getWavePath()}
            fill={`url(#gradient-${variant}-${gradient})`}
            className="opacity-30"
          />
          <path
            d={getWavePath()}
            fill="rgba(255,255,255,0.1)"
            transform="translate(0, 5)"
          />
        </svg>


        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full opacity-20"
              style={{
                left: `${15 + i * 15}%`,
                top: `${20 + (i % 3) * 20}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.2, 0.5, 0.2],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 3 + i * 0.5,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: i * 0.3,
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default SectionDivider;

  const renderDots = () => (
    <div className="relative h-12 flex items-center justify-center">
      <div className="flex space-x-2">
        {[...Array(7)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-3 h-3 rounded-full ${
              i === 3 ? 'bg-primary' : 
              i === 2 || i === 4 ? 'bg-secondary' : 'bg-accent'
            }`}
            initial={{ scale: 0, opacity: 0 }}
            animate={animate ? { scale: 1, opacity: 1 } : {}}
            transition={{ 
              delay: i * 0.2, 
              duration: 0.5,
              type: 'spring',
              stiffness: 200
            }}
            whileHover={{ scale: 1.5 }}
          />
        ))}
      </div>
      {/* Decorative strands */}
      <svg 
        viewBox="0 0 200 40" 
        className="absolute inset-0 w-full h-full pointer-events-none"
      >
        <motion.path
          d="M20,20 Q60,10 100,20 Q140,30 180,20"
          stroke="url(#dotGradient)"
          strokeWidth="1"
          fill="none"
          strokeDasharray="2,3"
          initial={{ pathLength: 0 }}
          animate={animate ? { pathLength: 1 } : {}}
          transition={{ delay: 1, duration: 1.5 }}
        />
        <defs>
          <linearGradient id="dotGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" className="stop-primary/20" />
            <stop offset="50%" className="stop-secondary/40" />
            <stop offset="100%" className="stop-accent/20" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  const renderFlowing = () => (
    <div className="relative h-20 overflow-hidden">
      <motion.div 
        className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent"
        initial={{ scaleX: 0 }}
        animate={animate ? { scaleX: 1 } : {}}
        transition={{ duration: 2 }}
      />
      <svg viewBox="0 0 1200 80" className="absolute inset-0 w-full h-full">
        {/* Flowing hair strands */}
        {[...Array(12)].map((_, i) => (
          <motion.path
            key={i}
            d={`M${i * 100},80 Q${i * 100 + 30},${20 + Math.sin(i * 0.5) * 15} ${i * 100 + 60},40 Q${i * 100 + 90},${60 + Math.cos(i * 0.3) * 10} ${i * 100 + 120},0`}
            stroke={`rgba(240, 98, 146, ${0.1 + (i % 4) * 0.1})`}
            strokeWidth="3"
            fill="none"
            strokeLinecap="round"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={animate ? { pathLength: 1, opacity: 1 } : {}}
            transition={{ delay: i * 0.15, duration: 2, ease: "easeInOut" }}
          />
        ))}
      </svg>
    </div>
  );

  const renderElegant = () => (
    <div className="relative h-16 flex items-center justify-center">
      <motion.div 
        className="absolute inset-0 bg-gradient-to-r from-transparent via-secondary/10 to-transparent"
        initial={{ opacity: 0 }}
        animate={animate ? { opacity: 1 } : {}}
        transition={{ duration: 1.5 }}
      />
      <div className="flex items-center space-x-6">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="flex items-center space-x-2"
            initial={{ opacity: 0, scale: 0 }}
            animate={animate ? { opacity: 1, scale: 1 } : {}}
            transition={{ delay: i * 0.3, duration: 0.8 }}
          >
            <motion.div
              className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full"
              animate={{ scale: [1, 1.3, 1] }}
              transition={{ duration: 2, repeat: Infinity, delay: i * 0.4 }}
            />
            {i < 4 && (
              <motion.div
                className="w-8 h-px bg-gradient-to-r from-primary/30 to-secondary/30"
                initial={{ scaleX: 0 }}
                animate={animate ? { scaleX: 1 } : {}}
                transition={{ delay: i * 0.3 + 0.5, duration: 0.6 }}
              />
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderSparkle = () => (
    <div className="relative h-24 overflow-hidden">
      <motion.div 
        className="absolute inset-0 bg-gradient-to-r from-transparent via-accent/5 to-transparent"
        initial={{ opacity: 0 }}
        animate={animate ? { opacity: 1 } : {}}
        transition={{ duration: 1 }}
      />
      {/* Sparkle effects */}
      {[...Array(15)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute"
          style={{
            left: `${10 + i * 6}%`,
            top: `${30 + Math.sin(i) * 20}%`,
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={animate ? { 
            opacity: [0, 1, 0], 
            scale: [0, 1, 0],
            rotate: [0, 180, 360]
          } : {}}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            delay: i * 0.2,
            repeatType: "reverse"
          }}
        >
          <svg width="16" height="16" viewBox="0 0 16 16" className="text-primary">
            <path d="M8 0l2 6h6l-4.5 3.5L13 16l-5-3.5L3 16l1.5-6.5L0 6h6L8 0z" fill="currentColor" />
          </svg>
        </motion.div>
      ))}
    </div>
  );

  const renderSilk = () => (
    <div className="relative h-20 overflow-hidden">
      <motion.div 
        className="absolute inset-0"
        initial={{ opacity: 0 }}
        animate={animate ? { opacity: 1 } : {}}
        transition={{ duration: 1.5 }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/10 to-accent/5" />
        <svg viewBox="0 0 1200 80" className="absolute inset-0 w-full h-full">
          {/* Silk-like flowing curves */}
          <motion.path
            d="M0,40 Q150,20 300,40 T600,40 T900,40 T1200,40"
            stroke="url(#silkGradient1)"
            strokeWidth="2"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={animate ? { pathLength: 1 } : {}}
            transition={{ duration: 3, ease: "easeInOut" }}
          />
          <motion.path
            d="M0,35 Q200,55 400,35 T800,35 T1200,35"
            stroke="url(#silkGradient2)"
            strokeWidth="1.5"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={animate ? { pathLength: 1 } : {}}
            transition={{ duration: 3, ease: "easeInOut", delay: 0.5 }}
          />
          <motion.path
            d="M0,45 Q100,25 200,45 T400,45 T600,45 T800,45 T1000,45 T1200,45"
            stroke="url(#silkGradient3)"
            strokeWidth="1"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={animate ? { pathLength: 1 } : {}}
            transition={{ duration: 3, ease: "easeInOut", delay: 1 }}
          />
          <defs>
            <linearGradient id="silkGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" className="stop-primary/20" />
              <stop offset="50%" className="stop-secondary/60" />
              <stop offset="100%" className="stop-accent/20" />
            </linearGradient>
            <linearGradient id="silkGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" className="stop-secondary/20" />
              <stop offset="50%" className="stop-accent/50" />
              <stop offset="100%" className="stop-primary/20" />
            </linearGradient>
            <linearGradient id="silkGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" className="stop-accent/20" />
              <stop offset="50%" className="stop-primary/40" />
              <stop offset="100%" className="stop-secondary/20" />
            </linearGradient>
          </defs>
        </svg>
      </motion.div>
    </div>
  );

  const renderDivider = () => {
    switch (variant) {
      case 'wave':
        return renderWave();
      case 'curl':
        return renderCurl();
      case 'braid':
        return renderBraid();
      case 'gradient':
        return renderGradient();
      case 'dots':
        return renderDots();
      case 'flowing':
        return renderFlowing();
      case 'elegant':
        return renderElegant();
      case 'sparkle':
        return renderSparkle();
      case 'silk':
        return renderSilk();
      default:
        return renderWave();
    }
  };

  return (
    <motion.div 
      className={baseClasses}
      initial={{ opacity: 0 }}
      animate={animate ? { opacity: 1 } : {}}
      transition={{ duration: 1 }}
    >
      {renderDivider()}
    </motion.div>
  );
};

export default SectionDivider; 