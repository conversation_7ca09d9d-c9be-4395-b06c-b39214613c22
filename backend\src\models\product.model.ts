import mongoose, { Document, Schema } from 'mongoose';

// Interface for AliExpress source information
export interface IAliExpressSource {
  id: string;
  url: string;
  originalPrice: number;
  supplierName?: string;
  supplierScore?: number;
  lastSyncDate: Date;
  storeId?: string; // AliExpress store ID this product was sourced from
}

// Interface for image with alt text and ordering
export interface IProductImage {
  _id?: mongoose.Types.ObjectId;
  url: string;
  alt: string;
  order: number;
  isDefault: boolean;
}

// Interface for product variant
export interface IProductVariant {
  color?: string;
  size?: string;
  style?: string;
  capSize?: string;
  density?: string;
  length?: string;
  sku: string;
  price: number;
  stock: number;
  images?: IProductImage[];
}

// Interface for product specifications
export interface IProductSpecifications {
  hairType?: string;
  length?: string;
  density?: string;
  capConstruction?: string;
  material?: string;
  texture?: string;
  [key: string]: any;
}

export interface IProduct extends Document {
  name: string;
  sku: string;
  description: string;
  price: number;
  salePrice?: number;
  aliExpressSource?: IAliExpressSource;
  category: Schema.Types.ObjectId;
  subcategory?: Schema.Types.ObjectId[];
  tags: string[];
  images: IProductImage[];
  variants: IProductVariant[];
  features: string[];
  specifications: IProductSpecifications;
  stock: number;
  isActive: boolean;
  reviewsCount: number;
  averageRating: number;
  version: number;
  provider: string;
  externalId?: string;
  createdAt: Date;
  updatedAt: Date;
  discountPercentage?: number;
}

const aliExpressSourceSchema = new Schema<IAliExpressSource>({
  id: {
    type: String,
    required: [true, 'AliExpress product ID is required'],
  },
  url: {
    type: String,
    required: [true, 'AliExpress product URL is required'],
  },
  originalPrice: {
    type: Number,
    required: [true, 'AliExpress original price is required'],
  },
  supplierName: String,
  supplierScore: {
    type: Number,
    min: [0, 'Supplier score must be between 0 and 5'],
    max: [5, 'Supplier score must be between 0 and 5'],
  },
  lastSyncDate: {
    type: Date,
    default: Date.now,
  },
  storeId: {
    type: String,
    index: true, // Add index for faster queries
  }
});

const productImageSchema = new Schema<IProductImage>({
  url: {
    type: String,
    required: [true, 'Image URL is required'],
  },
  alt: {
    type: String,
    default: '',
  },
  order: {
    type: Number,
    default: 0,
  },
  isDefault: {
    type: Boolean,
    default: false,
  },
});

const productVariantSchema = new Schema<IProductVariant>({
  color: String,
  size: String,
  style: String,
  capSize: String,
  density: String,
  length: String,
  sku: {
    type: String,
    required: [true, 'A variant must have a SKU'],
  },
  price: {
    type: Number,
    required: [true, 'A variant must have a price'],
    min: [0, 'Price must be above 0'],
  },
  stock: {
    type: Number,
    required: [true, 'A variant must have stock information'],
    min: [0, 'Stock cannot be negative'],
    default: 0,
  },
  images: {
    type: [productImageSchema],
    default: [],
  },
});

const productSchema = new Schema<IProduct>(
  {
    name: {
      type: String,
      required: [true, 'A product must have a name'],
      trim: true,
      maxlength: [100, 'A product name must have less or equal than 100 characters'],
    },
    sku: {
      type: String,
      required: [true, 'A product must have a SKU'],
      unique: true,
      trim: true,
      // index defined below with schema.index()
    },
    description: {
      type: String,
      required: [true, 'A product must have a description'],
    },
    price: {
      type: Number,
      required: [true, 'A product must have a price'],
      min: [0, 'Price must be above 0'],
    },
    salePrice: {
      type: Number,
      validate: {
        validator: function(this: IProduct, val: number) {
          // 'this' is only available when NOT using arrow function
          return val < this.price;
        },
        message: 'Sale price ({VALUE}) should be below regular price',
      },
    },
    aliExpressSource: {
      type: aliExpressSourceSchema,
      default: null,
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: 'Category',
      required: [true, 'A product must have a category'],
    },
    subcategory: [{
      type: Schema.Types.ObjectId,
      ref: 'Category',
    }],
    tags: [String],
    images: {
      type: [productImageSchema],
      validate: {
        validator: function(val: IProductImage[]) {
          return val.length > 0;
        },
        message: 'A product must have at least one image',
      },
    },
    variants: [productVariantSchema],
    features: [String],
    specifications: {
      type: Schema.Types.Mixed,
      default: {},
    },
    stock: {
      type: Number,
      required: [true, 'A product must have stock information'],
      min: [0, 'Stock cannot be negative'],
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    reviewsCount: {
      type: Number,
      default: 0,
    },
    averageRating: {
      type: Number,
      default: 0,
      min: [0, 'Rating must be above 0.0'],
      max: [5, 'Rating must be below 5.0'],
      set: (val: number) => Math.round(val * 10) / 10, // Round to 1 decimal place
    },
    version: {
      type: Number,
      default: 1,
    },
    provider: {
      type: String,
      required: [true, 'A product must have a provider'],
      default: 'default',
      enum: ['default', 'aliexpress', 'manual'],
    },
    externalId: String,
    // Stripe integration fields
    stripeProductId: {
      type: String,
      index: true,
    },
    stripePriceId: {
      type: String,
      index: true,
    },
    lastStripeSync: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ price: 1 });
productSchema.index({ category: 1 });
productSchema.index({ 'variants.sku': 1 });
productSchema.index({ isActive: 1 });
productSchema.index({ 'aliExpressSource.id': 1 });

// Ensure only one default image
productSchema.pre('save', function(next) {
  if (this.isModified('images')) {
    const defaultImages = this.images.filter((img: IProductImage) => img.isDefault);
    if (defaultImages.length > 1) {
      // Keep only the last image as default
      for (let i = 0; i < defaultImages.length - 1; i++) {
        // Only proceed if defaultImages[i]._id is defined
        if (defaultImages[i]._id) {
          const index = this.images.findIndex((img: IProductImage) => {
            // Only compare if img._id exists
            if (img._id && defaultImages[i]._id) {
              return img._id.toString() === defaultImages[i]._id?.toString();
            }
            return false;
          });
          if (index !== -1) {
            this.images[index].isDefault = false;
          }
        }
      }
    } else if (defaultImages.length === 0 && this.images.length > 0) {
      // Set the first image as default if none is set
      this.images[0].isDefault = true;
    }
  }
  next();
});

// Increment version on update
productSchema.pre('save', function(next) {
  if (!this.isNew) {
    this.version += 1;
  }
  next();
});

// Virtual for discounted percentage
productSchema.virtual('discountPercentage').get(function(this: IProduct) {
  if (this.salePrice) {
    return Math.round(((this.price - this.salePrice) / this.price) * 100);
  }
  return 0;
});

// Virtual for reviews (will populate from Review model)
productSchema.virtual('reviews', {
  ref: 'Review',
  foreignField: 'product',
  localField: '_id',
});

// Method to get default image
productSchema.methods.getDefaultImage = function() {
  const defaultImage = this.images.find((img: IProductImage) => img.isDefault);
  return defaultImage || (this.images.length > 0 ? this.images[0] : null);
};

// Method to check if product is on sale
productSchema.methods.isOnSale = function() {
  return this.salePrice !== undefined && this.salePrice < this.price;
};

const Product = mongoose.model<IProduct>('Product', productSchema);

export default Product; 