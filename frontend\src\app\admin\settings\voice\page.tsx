'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  MicrophoneIcon,
  SpeakerWaveIcon,
  LanguageIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  PlayIcon,
  StopIcon
} from '@heroicons/react/24/outline';

interface VoiceConfig {
  enabled: boolean;
  speechToText: {
    enabled: boolean;
    provider: string;
    language: string;
    autoDetectLanguage: boolean;
    noiseReduction: boolean;
    punctuation: boolean;
  };
  textToSpeech: {
    enabled: boolean;
    provider: string;
    voice: string;
    speed: number;
    pitch: number;
    volume: number;
    language: string;
  };
  voiceCommands: {
    enabled: boolean;
    wakeWord: string;
    commands: Array<{
      phrase: string;
      action: string;
      response: string;
    }>;
  };
  quality: {
    sampleRate: number;
    bitRate: number;
    format: string;
  };
}

export default function VoiceConfigPage() {
  const [config, setConfig] = useState<VoiceConfig>({
    enabled: true,
    speechToText: {
      enabled: true,
      provider: 'openai',
      language: 'auto',
      autoDetectLanguage: true,
      noiseReduction: true,
      punctuation: true
    },
    textToSpeech: {
      enabled: true,
      provider: 'openai',
      voice: 'alloy',
      speed: 1.0,
      pitch: 1.0,
      volume: 0.8,
      language: 'auto'
    },
    voiceCommands: {
      enabled: true,
      wakeWord: 'Hey Pelucas',
      commands: [
        { phrase: 'show me wigs', action: 'navigate', response: 'Here are our latest wigs!' },
        { phrase: 'check my order', action: 'order_status', response: 'Let me check your order status.' },
        { phrase: 'help me choose', action: 'recommendation', response: 'I\'d be happy to help you choose the perfect wig!' }
      ]
    },
    quality: {
      sampleRate: 44100,
      bitRate: 128,
      format: 'mp3'
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [playing, setPlaying] = useState(false);

  const providerOptions = [
    { value: 'openai', label: 'OpenAI Whisper/TTS' },
    { value: 'google', label: 'Google Cloud Speech' },
    { value: 'azure', label: 'Azure Cognitive Services' },
    { value: 'aws', label: 'Amazon Polly/Transcribe' }
  ];

  const voiceOptions = [
    { value: 'alloy', label: 'Alloy (Neutral)' },
    { value: 'echo', label: 'Echo (Male)' },
    { value: 'fable', label: 'Fable (British Male)' },
    { value: 'onyx', label: 'Onyx (Male)' },
    { value: 'nova', label: 'Nova (Female)' },
    { value: 'shimmer', label: 'Shimmer (Female)' }
  ];

  const languageOptions = [
    { value: 'auto', label: 'Auto-detect' },
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' }
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/admin/config/voice', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching voice config:', error);
      toast.error('Failed to load voice configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch('/api/admin/config/voice', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('Voice configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating voice config:', error);
      toast.error('Failed to update voice configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch('/api/admin/api-keys/voice/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error testing voice:', error);
      toast.error('Failed to test voice connection');
    } finally {
      setTesting(false);
    }
  };

  const handleTestVoice = async () => {
    setPlaying(true);
    
    try {
      const response = await fetch('/api/admin/voice/test-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          text: 'Hello! Welcome to Pelucas Chic. I\'m your voice assistant, ready to help you find the perfect wig.',
          voice: config.textToSpeech.voice,
          speed: config.textToSpeech.speed
        })
      });
      
      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        
        audio.onended = () => {
          setPlaying(false);
          URL.revokeObjectURL(audioUrl);
        };
        
        await audio.play();
        toast.success('Voice test played successfully');
      } else {
        throw new Error('Failed to generate voice test');
      }
    } catch (error) {
      console.error('Error testing voice:', error);
      toast.error('Failed to test voice');
      setPlaying(false);
    }
  };

  const addCommand = () => {
    setConfig(prev => ({
      ...prev,
      voiceCommands: {
        ...prev.voiceCommands,
        commands: [
          ...prev.voiceCommands.commands,
          { phrase: '', action: '', response: '' }
        ]
      }
    }));
  };

  const updateCommand = (index: number, field: 'phrase' | 'action' | 'response', value: string) => {
    setConfig(prev => ({
      ...prev,
      voiceCommands: {
        ...prev.voiceCommands,
        commands: prev.voiceCommands.commands.map((cmd, i) => 
          i === index ? { ...cmd, [field]: value } : cmd
        )
      }
    }));
  };

  const removeCommand = (index: number) => {
    setConfig(prev => ({
      ...prev,
      voiceCommands: {
        ...prev.voiceCommands,
        commands: prev.voiceCommands.commands.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Voice Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure speech-to-text, text-to-speech, and voice commands
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleTestVoice}
            disabled={playing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            {playing ? (
              <>
                <StopIcon className="h-4 w-4 mr-2" />
                Playing...
              </>
            ) : (
              <>
                <PlayIcon className="h-4 w-4 mr-2" />
                Test Voice
              </>
            )}
          </button>
          <button
            onClick={handleTest}
            disabled={testing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            {testing ? (
              <>
                <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
                Testing...
              </>
            ) : (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Test Connection
              </>
            )}
          </button>
        </div>
      </div>

      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <MicrophoneIcon className="h-5 w-5 mr-2" />
            Voice Features
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable Voice Features</h3>
              <p className="text-sm text-gray-500">
                Turn on/off voice processing capabilities
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
