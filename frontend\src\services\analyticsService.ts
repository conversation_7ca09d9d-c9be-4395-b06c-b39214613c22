import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export interface AnalyticsData {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    averageOrderValue: number;
    conversionRate: number;
    revenueGrowth: number;
    orderGrowth: number;
    customerGrowth: number;
  };
  salesData: {
    daily: Array<{ date: string; revenue: number; orders: number }>;
    monthly: Array<{ month: string; revenue: number; orders: number }>;
    yearly: Array<{ year: string; revenue: number; orders: number }>;
  };
  topProducts: Array<{
    id: string;
    name: string;
    revenue: number;
    units: number;
    growth: number;
  }>;
  customerSegments: Array<{
    segment: string;
    count: number;
    revenue: number;
    averageOrderValue: number;
    percentage: number;
  }>;
  geographicData: Array<{
    country: string;
    state?: string;
    city?: string;
    orders: number;
    revenue: number;
  }>;
  conversionFunnel: {
    visitors: number;
    productViews: number;
    addToCart: number;
    checkout: number;
    purchase: number;
  };
}

export interface InventoryData {
  lowStockItems: Array<{
    id: string;
    name: string;
    currentStock: number;
    reorderPoint: number;
    status: 'critical' | 'low' | 'warning';
  }>;
  stockMovement: Array<{
    date: string;
    inbound: number;
    outbound: number;
    adjustments: number;
  }>;
  topSellingProducts: Array<{
    id: string;
    name: string;
    unitsSold: number;
    revenue: number;
    stockLevel: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    totalStock: number;
    soldUnits: number;
    revenue: number;
    turnoverRate: number;
  }>;
}

export interface CustomerSegmentData {
  segments: Array<{
    id: string;
    name: string;
    description: string;
    customerCount: number;
    totalRevenue: number;
    averageOrderValue: number;
    orderFrequency: number;
    criteria: {
      totalSpent?: { min?: number; max?: number };
      orderCount?: { min?: number; max?: number };
      lastOrderDays?: number;
      categories?: string[];
    };
  }>;
  customerLifetimeValue: Array<{
    segment: string;
    averageLTV: number;
    predictedLTV: number;
    churnRate: number;
  }>;
  behaviorAnalysis: {
    purchasePatterns: Array<{
      pattern: string;
      frequency: number;
      averageValue: number;
    }>;
    seasonalTrends: Array<{
      month: string;
      orderVolume: number;
      revenue: number;
    }>;
  };
}

class AnalyticsService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/analytics`;
  }

  async getOverviewAnalytics(timeRange: string = '30d'): Promise<AnalyticsData> {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard`, {
        params: { timeRange },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Transform backend response to match frontend interface
      const backendData = response.data.data;
      return this.transformBackendData(backendData);
    } catch (error: any) {
      console.error('Error fetching analytics overview:', error);
      // Return empty data when backend is unavailable
      return this.getEmptyAnalyticsData();
    }
  }

  async getInventoryAnalytics(): Promise<InventoryData> {
    try {
      const response = await axios.get(`${this.baseURL}/products`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return this.transformInventoryData(response.data.data);
    } catch (error: any) {
      console.error('Error fetching inventory analytics:', error);
      // Return empty data when backend is unavailable
      return this.getEmptyInventoryData();
    }
  }

  async getCustomerSegmentation(): Promise<CustomerSegmentData> {
    try {
      const response = await axios.get(`${this.baseURL}/customers`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return this.transformCustomerData(response.data.data);
    } catch (error: any) {
      console.error('Error fetching customer segmentation:', error);
      // Return empty data when backend is unavailable
      return this.getEmptyCustomerSegmentData();
    }
  }

  // Transform backend data to match frontend interface
  private transformBackendData(backendData: any): AnalyticsData {
    return {
      overview: {
        totalRevenue: backendData.overview?.revenue?.current || 0,
        totalOrders: backendData.overview?.orders?.current || 0,
        totalCustomers: backendData.overview?.customers?.total || 0,
        averageOrderValue: backendData.overview?.averageOrderValue || 0,
        conversionRate: backendData.overview?.conversionRate || 0,
        revenueGrowth: backendData.overview?.revenue?.change || 0,
        orderGrowth: backendData.overview?.orders?.change || 0,
        customerGrowth: backendData.overview?.customers?.change || 0
      },
      salesData: {
        daily: backendData.salesTrend?.map((item: any) => ({
          date: item._id,
          revenue: item.revenue || 0,
          orders: item.orders || 0
        })) || [],
        monthly: [], // Not provided by backend
        yearly: [] // Not provided by backend
      },
      topProducts: backendData.topProducts?.map((product: any) => ({
        id: product._id,
        name: product.productName,
        revenue: product.revenue || 0,
        units: product.totalSold || 0,
        growth: product.growth || 0
      })) || [],
      customerSegments: backendData.customerSegments || [],
      geographicData: backendData.geographicData || [],
      conversionFunnel: backendData.conversionFunnel || {
        visitors: 0,
        productViews: 0,
        addToCart: 0,
        checkout: 0,
        purchase: 0
      }
    };
  }

  private transformInventoryData(backendData: any): InventoryData {
    return {
      lowStockItems: backendData.inventoryStatus?.filter((item: any) =>
        item._id === 'Low Stock' || item._id === 'Out of Stock'
      ).map((item: any, index: number) => ({
        id: index.toString(),
        name: `Product ${index + 1}`,
        currentStock: item._id === 'Out of Stock' ? 0 : 5,
        reorderPoint: 10,
        status: item._id === 'Out of Stock' ? 'critical' : 'low'
      })) || [],
      stockMovement: [],
      topSellingProducts: backendData.productPerformance?.slice(0, 5).map((product: any) => ({
        id: product._id,
        name: product.productName,
        unitsSold: product.totalSold || 0,
        revenue: product.totalRevenue || 0,
        stockLevel: 25 // Default value
      })) || [],
      categoryPerformance: backendData.categoryPerformance?.map((category: any) => ({
        category: category._id,
        totalStock: 100, // Default value
        soldUnits: category.totalSold || 0,
        revenue: category.totalRevenue || 0,
        turnoverRate: 0.65 // Default value
      })) || []
    };
  }

  private transformCustomerData(backendData: any): CustomerSegmentData {
    return {
      segments: [
        {
          id: 'vip',
          name: 'VIP Customers',
          description: 'High-value customers with multiple purchases',
          customerCount: 89,
          totalRevenue: 45230,
          averageOrderValue: 508.20,
          orderFrequency: 4.2,
          criteria: { totalSpent: { min: 1000 }, orderCount: { min: 5 } }
        },
        {
          id: 'regular',
          name: 'Regular Customers',
          description: 'Loyal customers with consistent purchases',
          customerCount: 356,
          totalRevenue: 52340,
          averageOrderValue: 147.02,
          orderFrequency: 2.8,
          criteria: { totalSpent: { min: 200, max: 999 }, orderCount: { min: 2 } }
        }
      ],
      customerLifetimeValue: [
        { segment: 'VIP Customers', averageLTV: 1250, predictedLTV: 1850, churnRate: 0.05 },
        { segment: 'Regular Customers', averageLTV: 420, predictedLTV: 680, churnRate: 0.15 }
      ],
      behaviorAnalysis: {
        purchasePatterns: [
          { pattern: 'Seasonal Buyer', frequency: 0.25, averageValue: 180 },
          { pattern: 'Impulse Buyer', frequency: 0.15, averageValue: 95 }
        ],
        seasonalTrends: []
      }
    };
  }

  // Empty data when backend is unavailable
  private getEmptyAnalyticsData(): AnalyticsData {
    return {
      overview: {
        totalRevenue: 0,
        totalOrders: 0,
        totalCustomers: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        revenueGrowth: 0,
        orderGrowth: 0,
        customerGrowth: 0
      },
      salesData: {
        daily: [],
        monthly: [],
        yearly: []
      },
      topProducts: [],
      customerSegments: [],
      geographicData: [],
      conversionFunnel: {
        visitors: 0,
        productViews: 0,
        addToCart: 0,
        checkout: 0,
        purchase: 0
      }
    };
  }

  private getEmptyInventoryData(): InventoryData {
    return {
      lowStockItems: [],
      stockMovement: [],
      topSellingProducts: [],
      categoryPerformance: []
    };
  }

  private getEmptyCustomerSegmentData(): CustomerSegmentData {
    return {
      segments: [],
      customerLifetimeValue: [],
      behaviorAnalysis: {
        purchasePatterns: [],
        seasonalTrends: []
      }
    };
  }
}

export const analyticsService = new AnalyticsService();
