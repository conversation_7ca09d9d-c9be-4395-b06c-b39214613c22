import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export interface AnalyticsData {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    averageOrderValue: number;
    conversionRate: number;
    revenueGrowth: number;
    orderGrowth: number;
    customerGrowth: number;
  };
  salesData: {
    daily: Array<{ date: string; revenue: number; orders: number }>;
    monthly: Array<{ month: string; revenue: number; orders: number }>;
    yearly: Array<{ year: string; revenue: number; orders: number }>;
  };
  topProducts: Array<{
    id: string;
    name: string;
    revenue: number;
    units: number;
    growth: number;
  }>;
  customerSegments: Array<{
    segment: string;
    count: number;
    revenue: number;
    averageOrderValue: number;
    percentage: number;
  }>;
  geographicData: Array<{
    country: string;
    state?: string;
    city?: string;
    orders: number;
    revenue: number;
  }>;
  conversionFunnel: {
    visitors: number;
    productViews: number;
    addToCart: number;
    checkout: number;
    purchase: number;
  };
}

export interface InventoryData {
  lowStockItems: Array<{
    id: string;
    name: string;
    currentStock: number;
    reorderPoint: number;
    status: 'critical' | 'low' | 'warning';
  }>;
  stockMovement: Array<{
    date: string;
    inbound: number;
    outbound: number;
    adjustments: number;
  }>;
  topSellingProducts: Array<{
    id: string;
    name: string;
    unitsSold: number;
    revenue: number;
    stockLevel: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    totalStock: number;
    soldUnits: number;
    revenue: number;
    turnoverRate: number;
  }>;
}

export interface CustomerSegmentData {
  segments: Array<{
    id: string;
    name: string;
    description: string;
    customerCount: number;
    totalRevenue: number;
    averageOrderValue: number;
    orderFrequency: number;
    criteria: {
      totalSpent?: { min?: number; max?: number };
      orderCount?: { min?: number; max?: number };
      lastOrderDays?: number;
      categories?: string[];
    };
  }>;
  customerLifetimeValue: Array<{
    segment: string;
    averageLTV: number;
    predictedLTV: number;
    churnRate: number;
  }>;
  behaviorAnalysis: {
    purchasePatterns: Array<{
      pattern: string;
      frequency: number;
      averageValue: number;
    }>;
    seasonalTrends: Array<{
      month: string;
      orderVolume: number;
      revenue: number;
    }>;
  };
}

class AnalyticsService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/analytics`;
  }

  async getOverviewAnalytics(timeRange: string = '30d'): Promise<AnalyticsData> {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard`, {
        params: { timeRange },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Transform backend response to match frontend interface
      const backendData = response.data.data;
      return this.transformBackendData(backendData);
    } catch (error: any) {
      console.error('Error fetching analytics overview:', error);
      // Return mock data for development
      return this.getMockAnalyticsData();
    }
  }

  async getInventoryAnalytics(): Promise<InventoryData> {
    try {
      const response = await axios.get(`${this.baseURL}/products`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return this.transformInventoryData(response.data.data);
    } catch (error: any) {
      console.error('Error fetching inventory analytics:', error);
      // Return mock data for development
      return this.getMockInventoryData();
    }
  }

  async getCustomerSegmentation(): Promise<CustomerSegmentData> {
    try {
      const response = await axios.get(`${this.baseURL}/customers`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return this.transformCustomerData(response.data.data);
    } catch (error: any) {
      console.error('Error fetching customer segmentation:', error);
      // Return mock data for development
      return this.getMockCustomerSegmentData();
    }
  }

  // Transform backend data to match frontend interface
  private transformBackendData(backendData: any): AnalyticsData {
    return {
      overview: {
        totalRevenue: backendData.overview?.revenue?.current || 0,
        totalOrders: backendData.overview?.orders?.current || 0,
        totalCustomers: backendData.overview?.customers?.total || 0,
        averageOrderValue: backendData.overview?.averageOrderValue || 0,
        conversionRate: 3.2, // Default value since not in backend
        revenueGrowth: backendData.overview?.revenue?.change || 0,
        orderGrowth: backendData.overview?.orders?.change || 0,
        customerGrowth: 8.9 // Default value since not in backend
      },
      salesData: {
        daily: backendData.salesTrend?.map((item: any) => ({
          date: item._id,
          revenue: item.revenue || 0,
          orders: item.orders || 0
        })) || [],
        monthly: [], // Not provided by backend
        yearly: [] // Not provided by backend
      },
      topProducts: backendData.topProducts?.map((product: any) => ({
        id: product._id,
        name: product.productName,
        revenue: product.revenue || 0,
        units: product.totalSold || 0,
        growth: 15.0 // Default value since not in backend
      })) || [],
      customerSegments: [
        { segment: 'VIP Customers', count: 89, revenue: 45230, averageOrderValue: 508.20, percentage: 10 },
        { segment: 'Regular Customers', count: 356, revenue: 52340, averageOrderValue: 147.02, percentage: 40 },
        { segment: 'New Customers', count: backendData.overview?.customers?.new || 0, revenue: 18960, averageOrderValue: 71.01, percentage: 30 },
        { segment: 'Inactive Customers', count: 180, revenue: 8900, averageOrderValue: 49.44, percentage: 20 }
      ],
      geographicData: [
        { country: 'United States', orders: 856, revenue: 89540 },
        { country: 'Canada', orders: 234, revenue: 23450 }
      ],
      conversionFunnel: {
        visitors: 15420,
        productViews: 8930,
        addToCart: 2340,
        checkout: 1560,
        purchase: backendData.overview?.orders?.current || 0
      }
    };
  }

  private transformInventoryData(backendData: any): InventoryData {
    return {
      lowStockItems: backendData.inventoryStatus?.filter((item: any) =>
        item._id === 'Low Stock' || item._id === 'Out of Stock'
      ).map((item: any, index: number) => ({
        id: index.toString(),
        name: `Product ${index + 1}`,
        currentStock: item._id === 'Out of Stock' ? 0 : 5,
        reorderPoint: 10,
        status: item._id === 'Out of Stock' ? 'critical' : 'low'
      })) || [],
      stockMovement: [],
      topSellingProducts: backendData.productPerformance?.slice(0, 5).map((product: any) => ({
        id: product._id,
        name: product.productName,
        unitsSold: product.totalSold || 0,
        revenue: product.totalRevenue || 0,
        stockLevel: 25 // Default value
      })) || [],
      categoryPerformance: backendData.categoryPerformance?.map((category: any) => ({
        category: category._id,
        totalStock: 100, // Default value
        soldUnits: category.totalSold || 0,
        revenue: category.totalRevenue || 0,
        turnoverRate: 0.65 // Default value
      })) || []
    };
  }

  private transformCustomerData(backendData: any): CustomerSegmentData {
    return {
      segments: [
        {
          id: 'vip',
          name: 'VIP Customers',
          description: 'High-value customers with multiple purchases',
          customerCount: 89,
          totalRevenue: 45230,
          averageOrderValue: 508.20,
          orderFrequency: 4.2,
          criteria: { totalSpent: { min: 1000 }, orderCount: { min: 5 } }
        },
        {
          id: 'regular',
          name: 'Regular Customers',
          description: 'Loyal customers with consistent purchases',
          customerCount: 356,
          totalRevenue: 52340,
          averageOrderValue: 147.02,
          orderFrequency: 2.8,
          criteria: { totalSpent: { min: 200, max: 999 }, orderCount: { min: 2 } }
        }
      ],
      customerLifetimeValue: [
        { segment: 'VIP Customers', averageLTV: 1250, predictedLTV: 1850, churnRate: 0.05 },
        { segment: 'Regular Customers', averageLTV: 420, predictedLTV: 680, churnRate: 0.15 }
      ],
      behaviorAnalysis: {
        purchasePatterns: [
          { pattern: 'Seasonal Buyer', frequency: 0.25, averageValue: 180 },
          { pattern: 'Impulse Buyer', frequency: 0.15, averageValue: 95 }
        ],
        seasonalTrends: []
      }
    };
  }

  // Mock data for development
  private getMockAnalyticsData(): AnalyticsData {
    return {
      overview: {
        totalRevenue: 125430,
        totalOrders: 1247,
        totalCustomers: 892,
        averageOrderValue: 100.58,
        conversionRate: 3.2,
        revenueGrowth: 15.3,
        orderGrowth: 12.7,
        customerGrowth: 8.9
      },
      salesData: {
        daily: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          revenue: Math.floor(Math.random() * 5000) + 1000,
          orders: Math.floor(Math.random() * 50) + 10
        })),
        monthly: Array.from({ length: 12 }, (_, i) => ({
          month: new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'short' }),
          revenue: Math.floor(Math.random() * 50000) + 20000,
          orders: Math.floor(Math.random() * 500) + 100
        })),
        yearly: [
          { year: '2022', revenue: 450000, orders: 4200 },
          { year: '2023', revenue: 620000, orders: 5800 },
          { year: '2024', revenue: 780000, orders: 7200 }
        ]
      },
      topProducts: [
        { id: '1', name: 'Premium Lace Front Wig - Black', revenue: 15420, units: 154, growth: 23.5 },
        { id: '2', name: 'Human Hair Bob - Blonde', revenue: 12350, units: 123, growth: 18.2 },
        { id: '3', name: 'Curly Full Lace Wig - Brown', revenue: 9870, units: 98, growth: 15.7 },
        { id: '4', name: 'Straight Long Wig - Auburn', revenue: 8540, units: 85, growth: 12.3 },
        { id: '5', name: 'Wavy Medium Wig - Platinum', revenue: 7230, units: 72, growth: 9.8 }
      ],
      customerSegments: [
        { segment: 'VIP Customers', count: 89, revenue: 45230, averageOrderValue: 508.20, percentage: 10 },
        { segment: 'Regular Customers', count: 356, revenue: 52340, averageOrderValue: 147.02, percentage: 40 },
        { segment: 'New Customers', count: 267, revenue: 18960, averageOrderValue: 71.01, percentage: 30 },
        { segment: 'Inactive Customers', count: 180, revenue: 8900, averageOrderValue: 49.44, percentage: 20 }
      ],
      geographicData: [
        { country: 'United States', orders: 856, revenue: 89540 },
        { country: 'Canada', orders: 234, revenue: 23450 },
        { country: 'United Kingdom', orders: 123, revenue: 12340 },
        { country: 'Australia', orders: 34, revenue: 3450 }
      ],
      conversionFunnel: {
        visitors: 15420,
        productViews: 8930,
        addToCart: 2340,
        checkout: 1560,
        purchase: 1247
      }
    };
  }

  private getMockInventoryData(): InventoryData {
    return {
      lowStockItems: [
        { id: '1', name: 'Premium Lace Front Wig - Black', currentStock: 5, reorderPoint: 10, status: 'critical' },
        { id: '2', name: 'Human Hair Bob - Blonde', currentStock: 8, reorderPoint: 15, status: 'low' },
        { id: '3', name: 'Curly Full Lace Wig - Brown', currentStock: 12, reorderPoint: 20, status: 'warning' }
      ],
      stockMovement: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        inbound: Math.floor(Math.random() * 50) + 10,
        outbound: Math.floor(Math.random() * 40) + 5,
        adjustments: Math.floor(Math.random() * 10) - 5
      })),
      topSellingProducts: [
        { id: '1', name: 'Premium Lace Front Wig - Black', unitsSold: 154, revenue: 15420, stockLevel: 25 },
        { id: '2', name: 'Human Hair Bob - Blonde', unitsSold: 123, revenue: 12350, stockLevel: 18 },
        { id: '3', name: 'Curly Full Lace Wig - Brown', unitsSold: 98, revenue: 9870, stockLevel: 32 }
      ],
      categoryPerformance: [
        { category: 'Lace Front Wigs', totalStock: 245, soldUnits: 189, revenue: 23450, turnoverRate: 0.77 },
        { category: 'Full Lace Wigs', totalStock: 156, soldUnits: 98, revenue: 15670, turnoverRate: 0.63 },
        { category: 'Synthetic Wigs', totalStock: 389, soldUnits: 234, revenue: 12340, turnoverRate: 0.60 }
      ]
    };
  }

  private getMockCustomerSegmentData(): CustomerSegmentData {
    return {
      segments: [
        {
          id: 'vip',
          name: 'VIP Customers',
          description: 'High-value customers with multiple purchases',
          customerCount: 89,
          totalRevenue: 45230,
          averageOrderValue: 508.20,
          orderFrequency: 4.2,
          criteria: { totalSpent: { min: 1000 }, orderCount: { min: 5 } }
        },
        {
          id: 'regular',
          name: 'Regular Customers',
          description: 'Loyal customers with consistent purchases',
          customerCount: 356,
          totalRevenue: 52340,
          averageOrderValue: 147.02,
          orderFrequency: 2.8,
          criteria: { totalSpent: { min: 200, max: 999 }, orderCount: { min: 2 } }
        },
        {
          id: 'new',
          name: 'New Customers',
          description: 'Recent customers with 1-2 purchases',
          customerCount: 267,
          totalRevenue: 18960,
          averageOrderValue: 71.01,
          orderFrequency: 1.2,
          criteria: { orderCount: { max: 2 }, lastOrderDays: 90 }
        }
      ],
      customerLifetimeValue: [
        { segment: 'VIP Customers', averageLTV: 1250, predictedLTV: 1850, churnRate: 0.05 },
        { segment: 'Regular Customers', averageLTV: 420, predictedLTV: 680, churnRate: 0.15 },
        { segment: 'New Customers', averageLTV: 85, predictedLTV: 250, churnRate: 0.35 }
      ],
      behaviorAnalysis: {
        purchasePatterns: [
          { pattern: 'Seasonal Buyer', frequency: 0.25, averageValue: 180 },
          { pattern: 'Impulse Buyer', frequency: 0.15, averageValue: 95 },
          { pattern: 'Research-Heavy Buyer', frequency: 0.35, averageValue: 220 },
          { pattern: 'Loyal Repeat Buyer', frequency: 0.25, averageValue: 165 }
        ],
        seasonalTrends: Array.from({ length: 12 }, (_, i) => ({
          month: new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'short' }),
          orderVolume: Math.floor(Math.random() * 200) + 50,
          revenue: Math.floor(Math.random() * 25000) + 5000
        }))
      }
    };
  }
}

export const analyticsService = new AnalyticsService();
