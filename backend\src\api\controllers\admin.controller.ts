import { Request, Response } from 'express';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import Order from '../../models/order.model';
import Product from '../../models/product.model';
import User from '../../models/user.model';
import { testAliExpressConnection } from '../../services/aliexpress/client';
import { testWhatsAppConnection } from '../../services/whatsapp/client';
import { testStripeConnection } from '../../services/payment/providers/stripe';
import { testPayPalConnection } from '../../services/payment/providers/paypal';
import { stripeProductSyncService } from '../../services/stripe-product-sync.service';

const execAsync = promisify(exec);

// Helper function to read environment files
const readEnvFile = async (filePath: string): Promise<Record<string, string>> => {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    const env: Record<string, string> = {};
    
    content.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    console.error(`Error reading env file ${filePath}:`, error);
    return {};
  }
};

// Helper function to write environment files
const writeEnvFile = async (filePath: string, env: Record<string, string>): Promise<void> => {
  const content = Object.entries(env)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  await fs.writeFile(filePath, content, 'utf-8');
};

// Get all API keys from environment files
export const getApiKeys = async (req: Request, res: Response) => {
  try {
    const envPaths = {
      backend: path.join(process.cwd(), '..', '.env'),
      frontend: path.join(process.cwd(), '..', 'frontend', '.env.local'),
      chatbot: path.join(process.cwd(), '..', 'chatbot', '.env'),
      whatsapp: path.join(process.cwd(), '..', 'services', 'whatsapp', '.env'),
      voice: path.join(process.cwd(), '..', 'services', 'voice', '.env')
    };

    const apiKeys: any = {
      aliexpress: {
        appKey: process.env.ALIEXPRESS_APP_KEY || '',
        appSecret: process.env.ALIEXPRESS_APP_SECRET ? '***' + process.env.ALIEXPRESS_APP_SECRET.slice(-4) : '',
        storeIds: process.env.ALIEXPRESS_STORE_IDS || ''
      },
      stripe: {
        publicKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '',
        secretKey: process.env.STRIPE_SECRET_KEY ? '***' + process.env.STRIPE_SECRET_KEY.slice(-4) : '',
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? '***' + process.env.STRIPE_WEBHOOK_SECRET.slice(-4) : ''
      },
      paypal: {
        clientId: process.env.PAYPAL_CLIENT_ID || '',
        clientSecret: process.env.PAYPAL_CLIENT_SECRET ? '***' + process.env.PAYPAL_CLIENT_SECRET.slice(-4) : '',
        mode: process.env.PAYPAL_MODE || 'sandbox'
      },
      whatsapp: {
        accessToken: process.env.WHATSAPP_ACCESS_TOKEN ? '***' + process.env.WHATSAPP_ACCESS_TOKEN.slice(-4) : '',
        phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID || '',
        businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID || '',
        webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN || ''
      },
      openai: {
        apiKey: process.env.OPENAI_API_KEY ? '***' + process.env.OPENAI_API_KEY.slice(-4) : '',
        model: process.env.OPENAI_MODEL || 'gpt-4'
      },
      google: {
        speechToTextKey: process.env.GOOGLE_SPEECH_TO_TEXT_KEY ? '***' + process.env.GOOGLE_SPEECH_TO_TEXT_KEY.slice(-4) : '',
        textToSpeechKey: process.env.GOOGLE_TEXT_TO_SPEECH_KEY ? '***' + process.env.GOOGLE_TEXT_TO_SPEECH_KEY.slice(-4) : '',
        vertexAIKey: process.env.GOOGLE_VERTEX_AI_KEY ? '***' + process.env.GOOGLE_VERTEX_AI_KEY.slice(-4) : ''
      },
      instagram: {
        accessToken: process.env.INSTAGRAM_ACCESS_TOKEN ? '***' + process.env.INSTAGRAM_ACCESS_TOKEN.slice(-4) : '',
        businessAccountId: process.env.INSTAGRAM_BUSINESS_ACCOUNT_ID || ''
      },
      email: {
        sendgridKey: process.env.SENDGRID_API_KEY ? '***' + process.env.SENDGRID_API_KEY.slice(-4) : '',
        fromEmail: process.env.EMAIL_FROM || '',
        fromName: process.env.EMAIL_FROM_NAME || ''
      },
      ngrok: {
        authToken: process.env.NGROK_AUTHTOKEN ? '***' + process.env.NGROK_AUTHTOKEN.slice(-4) : '',
        apiKey: process.env.NGROK_API_KEY ? '***' + process.env.NGROK_API_KEY.slice(-4) : ''
      }
    };

    res.json({ success: true, apiKeys });
  } catch (error) {
    console.error('Error fetching API keys:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch API keys' });
  }
};

// Update API key for a specific service
export const updateApiKey = async (req: Request, res: Response) => {
  try {
    const { service } = req.params;
    const updates = req.body;
    
    // Map of which env file to update for each service
    const envFileMap: Record<string, string[]> = {
      aliexpress: ['backend/.env', 'frontend/.env.local'],
      stripe: ['backend/.env', 'frontend/.env.local'],
      paypal: ['backend/.env', 'frontend/.env.local'],
      whatsapp: ['backend/.env', 'services/whatsapp/.env'],
      openai: ['backend/.env', 'chatbot/.env'],
      google: ['backend/.env', 'chatbot/.env', 'services/voice/.env'],
      instagram: ['backend/.env', 'frontend/.env.local'],
      email: ['backend/.env'],
      ngrok: ['backend/.env', 'services/whatsapp/.env']
    };

    const filesToUpdate = envFileMap[service] || [];
    
    for (const file of filesToUpdate) {
      const envPath = path.join(process.cwd(), '..', file);
      const env = await readEnvFile(envPath);
      
      // Update the specific keys
      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined && value !== null && !String(value).includes('***')) {
          env[key] = String(value);
        }
      });
      
      await writeEnvFile(envPath, env);
    }
    
    // Restart services if needed
    if (['aliexpress', 'stripe', 'paypal'].includes(service)) {
      // These require backend restart
      console.log('API keys updated. Backend restart may be required.');
    }
    
    res.json({ success: true, message: `${service} API keys updated successfully` });
  } catch (error) {
    console.error('Error updating API keys:', error);
    res.status(500).json({ success: false, error: 'Failed to update API keys' });
  }
};

// Test API key connection
export const testApiKey = async (req: Request, res: Response) => {
  try {
    const { service } = req.params;
    
    let result = { success: false, message: 'Unknown service' };
    
    switch (service) {
      case 'aliexpress':
        result = await testAliExpressConnection();
        break;
      case 'stripe':
        result = await testStripeConnection();
        break;
      case 'paypal':
        result = await testPayPalConnection();
        break;
      case 'whatsapp':
        result = await testWhatsAppConnection();
        break;
      case 'openai':
        // Test OpenAI connection
        const { OpenAI } = await import('openai');
        const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
        try {
          await openai.models.list();
          result = { success: true, message: 'OpenAI connection successful' };
        } catch (error) {
          result = { success: false, message: 'OpenAI connection failed' };
        }
        break;
      default:
        result = { success: false, message: `Test not implemented for ${service}` };
    }
    
    res.json(result);
  } catch (error) {
    console.error('Error testing API key:', error);
    res.status(500).json({ success: false, error: 'Failed to test API key' });
  }
};

// Get dashboard statistics
export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    
    // Get order statistics
    const totalOrders = await Order.countDocuments();
    const monthlyOrders = await Order.countDocuments({ createdAt: { $gte: startOfMonth } });
    const weeklyOrders = await Order.countDocuments({ createdAt: { $gte: startOfWeek } });
    const pendingOrders = await Order.countDocuments({ status: 'pending' });
    
    // Get revenue statistics
    const totalRevenue = await Order.aggregate([
      { $match: { status: { $ne: 'cancelled' } } },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]);
    
    const monthlyRevenue = await Order.aggregate([
      { $match: { status: { $ne: 'cancelled' }, createdAt: { $gte: startOfMonth } } },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]);
    
    // Get product statistics
    const totalProducts = await Product.countDocuments();
    const activeProducts = await Product.countDocuments({ status: 'active' });
    const outOfStockProducts = await Product.countDocuments({ stock: 0 });
    
    // Get customer statistics
    const totalCustomers = await User.countDocuments({ role: 'customer' });
    const newCustomersThisMonth = await User.countDocuments({ 
      role: 'customer', 
      createdAt: { $gte: startOfMonth } 
    });
    
    // Get recent activity
    const recentOrders = await Order.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('user', 'name email');
    
    const stats = {
      orders: {
        total: totalOrders,
        monthly: monthlyOrders,
        weekly: weeklyOrders,
        pending: pendingOrders
      },
      revenue: {
        total: totalRevenue[0]?.total || 0,
        monthly: monthlyRevenue[0]?.total || 0
      },
      products: {
        total: totalProducts,
        active: activeProducts,
        outOfStock: outOfStockProducts
      },
      customers: {
        total: totalCustomers,
        newThisMonth: newCustomersThisMonth
      },
      recentActivity: recentOrders
    };
    
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch dashboard statistics' });
  }
};

// Get system configuration
export const getSystemConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'Pelucas Chic Human Hair',
      siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://pelucaschichumanhair.com',
      supportEmail: process.env.SUPPORT_EMAIL || '',
      timezone: process.env.TZ || 'UTC',
      currency: process.env.CURRENCY || 'USD',
      language: process.env.DEFAULT_LANGUAGE || 'en',
      maintenanceMode: process.env.MAINTENANCE_MODE === 'true'
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching system config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch system configuration' });
  }
};

// Update system configuration
export const updateSystemConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    // Update backend env
    const backendEnvPath = path.join(process.cwd(), '..', '.env');
    const backendEnv = await readEnvFile(backendEnvPath);
    
    // Update frontend env
    const frontendEnvPath = path.join(process.cwd(), '..', 'frontend', '.env.local');
    const frontendEnv = await readEnvFile(frontendEnvPath);
    
    // Map config to env variables
    if (updates.siteName !== undefined) {
      frontendEnv.NEXT_PUBLIC_SITE_NAME = updates.siteName;
    }
    if (updates.siteUrl !== undefined) {
      frontendEnv.NEXT_PUBLIC_SITE_URL = updates.siteUrl;
    }
    if (updates.supportEmail !== undefined) {
      backendEnv.SUPPORT_EMAIL = updates.supportEmail;
    }
    if (updates.timezone !== undefined) {
      backendEnv.TZ = updates.timezone;
    }
    if (updates.currency !== undefined) {
      backendEnv.CURRENCY = updates.currency;
    }
    if (updates.language !== undefined) {
      backendEnv.DEFAULT_LANGUAGE = updates.language;
    }
    if (updates.maintenanceMode !== undefined) {
      backendEnv.MAINTENANCE_MODE = updates.maintenanceMode.toString();
    }
    
    await writeEnvFile(backendEnvPath, backendEnv);
    await writeEnvFile(frontendEnvPath, frontendEnv);
    
    res.json({ success: true, message: 'System configuration updated successfully' });
  } catch (error) {
    console.error('Error updating system config:', error);
    res.status(500).json({ success: false, error: 'Failed to update system configuration' });
  }
};

// Get WhatsApp configuration
export const getWhatsAppConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      enabled: process.env.WHATSAPP_ENABLED === 'true',
      phoneNumber: process.env.WHATSAPP_PHONE_NUMBER || '',
      businessName: process.env.WHATSAPP_BUSINESS_NAME || '',
      webhookUrl: process.env.WHATSAPP_WEBHOOK_URL || '',
      welcomeMessage: process.env.WHATSAPP_WELCOME_MESSAGE || 'Welcome to Pelucas Chic! How can I help you today?',
      offlineMessage: process.env.WHATSAPP_OFFLINE_MESSAGE || 'Thank you for your message. We will get back to you during business hours.',
      businessHours: {
        start: process.env.BUSINESS_HOURS_START || '09:00',
        end: process.env.BUSINESS_HOURS_END || '18:00',
        timezone: process.env.BUSINESS_TIMEZONE || 'America/New_York',
        workDays: process.env.BUSINESS_WORK_DAYS || 'Mon-Fri'
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching WhatsApp config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch WhatsApp configuration' });
  }
};

// Update WhatsApp configuration
export const updateWhatsAppConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const whatsappEnvPath = path.join(process.cwd(), '..', 'services', 'whatsapp', '.env');
    const whatsappEnv = await readEnvFile(whatsappEnvPath);
    
    if (updates.enabled !== undefined) {
      whatsappEnv.WHATSAPP_ENABLED = updates.enabled.toString();
    }
    if (updates.phoneNumber !== undefined) {
      whatsappEnv.WHATSAPP_PHONE_NUMBER = updates.phoneNumber;
    }
    if (updates.businessName !== undefined) {
      whatsappEnv.WHATSAPP_BUSINESS_NAME = updates.businessName;
    }
    if (updates.webhookUrl !== undefined) {
      whatsappEnv.WHATSAPP_WEBHOOK_URL = updates.webhookUrl;
    }
    if (updates.welcomeMessage !== undefined) {
      whatsappEnv.WHATSAPP_WELCOME_MESSAGE = updates.welcomeMessage;
    }
    if (updates.offlineMessage !== undefined) {
      whatsappEnv.WHATSAPP_OFFLINE_MESSAGE = updates.offlineMessage;
    }
    if (updates.businessHours) {
      if (updates.businessHours.start) whatsappEnv.BUSINESS_HOURS_START = updates.businessHours.start;
      if (updates.businessHours.end) whatsappEnv.BUSINESS_HOURS_END = updates.businessHours.end;
      if (updates.businessHours.timezone) whatsappEnv.BUSINESS_TIMEZONE = updates.businessHours.timezone;
      if (updates.businessHours.workDays) whatsappEnv.BUSINESS_WORK_DAYS = updates.businessHours.workDays;
    }
    
    await writeEnvFile(whatsappEnvPath, whatsappEnv);
    
    res.json({ success: true, message: 'WhatsApp configuration updated successfully' });
  } catch (error) {
    console.error('Error updating WhatsApp config:', error);
    res.status(500).json({ success: false, error: 'Failed to update WhatsApp configuration' });
  }
};

// Get Chatbot configuration
export const getChatbotConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      enabled: process.env.CHATBOT_ENABLED !== 'false',
      provider: process.env.AI_PROVIDER || 'openai',
      model: process.env.AI_MODEL || 'gpt-4',
      temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.AI_MAX_TOKENS || '2000'),
      systemPrompt: process.env.CHATBOT_SYSTEM_PROMPT || 'You are a helpful assistant for a wig store...',
      welcomeMessage: process.env.CHATBOT_WELCOME_MESSAGE || 'Hello! How can I help you find the perfect wig today?',
      fallbackMessage: process.env.CHATBOT_FALLBACK_MESSAGE || 'I apologize, I didn\'t understand that. Could you please rephrase?',
      features: {
        productSearch: process.env.CHATBOT_PRODUCT_SEARCH === 'true',
        orderTracking: process.env.CHATBOT_ORDER_TRACKING === 'true',
        recommendations: process.env.CHATBOT_RECOMMENDATIONS === 'true',
        voiceSupport: process.env.CHATBOT_VOICE_SUPPORT === 'true'
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching chatbot config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch chatbot configuration' });
  }
};

// Update Chatbot configuration
export const updateChatbotConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const chatbotEnvPath = path.join(process.cwd(), '..', 'chatbot', '.env');
    const chatbotEnv = await readEnvFile(chatbotEnvPath);
    
    if (updates.enabled !== undefined) {
      chatbotEnv.CHATBOT_ENABLED = updates.enabled.toString();
    }
    if (updates.provider !== undefined) {
      chatbotEnv.AI_PROVIDER = updates.provider;
    }
    if (updates.model !== undefined) {
      chatbotEnv.AI_MODEL = updates.model;
    }
    if (updates.temperature !== undefined) {
      chatbotEnv.AI_TEMPERATURE = updates.temperature.toString();
    }
    if (updates.maxTokens !== undefined) {
      chatbotEnv.AI_MAX_TOKENS = updates.maxTokens.toString();
    }
    if (updates.systemPrompt !== undefined) {
      chatbotEnv.CHATBOT_SYSTEM_PROMPT = updates.systemPrompt;
    }
    if (updates.welcomeMessage !== undefined) {
      chatbotEnv.CHATBOT_WELCOME_MESSAGE = updates.welcomeMessage;
    }
    if (updates.fallbackMessage !== undefined) {
      chatbotEnv.CHATBOT_FALLBACK_MESSAGE = updates.fallbackMessage;
    }
    if (updates.features) {
      if (updates.features.productSearch !== undefined) {
        chatbotEnv.CHATBOT_PRODUCT_SEARCH = updates.features.productSearch.toString();
      }
      if (updates.features.orderTracking !== undefined) {
        chatbotEnv.CHATBOT_ORDER_TRACKING = updates.features.orderTracking.toString();
      }
      if (updates.features.recommendations !== undefined) {
        chatbotEnv.CHATBOT_RECOMMENDATIONS = updates.features.recommendations.toString();
      }
      if (updates.features.voiceSupport !== undefined) {
        chatbotEnv.CHATBOT_VOICE_SUPPORT = updates.features.voiceSupport.toString();
      }
    }
    
    await writeEnvFile(chatbotEnvPath, chatbotEnv);
    
    res.json({ success: true, message: 'Chatbot configuration updated successfully' });
  } catch (error) {
    console.error('Error updating chatbot config:', error);
    res.status(500).json({ success: false, error: 'Failed to update chatbot configuration' });
  }
};

// Get Payment configuration
export const getPaymentConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      stripe: {
        enabled: process.env.STRIPE_ENABLED !== 'false',
        mode: process.env.STRIPE_MODE || 'test',
        currency: process.env.STRIPE_CURRENCY || 'USD',
        captureMethod: process.env.STRIPE_CAPTURE_METHOD || 'automatic'
      },
      paypal: {
        enabled: process.env.PAYPAL_ENABLED !== 'false',
        mode: process.env.PAYPAL_MODE || 'sandbox',
        currency: process.env.PAYPAL_CURRENCY || 'USD'
      },
      tax: {
        enabled: process.env.TAX_ENABLED === 'true',
        rate: parseFloat(process.env.TAX_RATE || '0'),
        includedInPrice: process.env.TAX_INCLUDED_IN_PRICE === 'true'
      },
      shipping: {
        freeShippingThreshold: parseFloat(process.env.FREE_SHIPPING_THRESHOLD || '100'),
        flatRate: parseFloat(process.env.SHIPPING_FLAT_RATE || '10'),
        expressRate: parseFloat(process.env.SHIPPING_EXPRESS_RATE || '25')
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching payment config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch payment configuration' });
  }
};

// Update Payment configuration
export const updatePaymentConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const backendEnvPath = path.join(process.cwd(), '..', '.env');
    const backendEnv = await readEnvFile(backendEnvPath);
    
    if (updates.stripe) {
      if (updates.stripe.enabled !== undefined) {
        backendEnv.STRIPE_ENABLED = updates.stripe.enabled.toString();
      }
      if (updates.stripe.mode !== undefined) {
        backendEnv.STRIPE_MODE = updates.stripe.mode;
      }
      if (updates.stripe.currency !== undefined) {
        backendEnv.STRIPE_CURRENCY = updates.stripe.currency;
      }
      if (updates.stripe.captureMethod !== undefined) {
        backendEnv.STRIPE_CAPTURE_METHOD = updates.stripe.captureMethod;
      }
    }
    
    if (updates.paypal) {
      if (updates.paypal.enabled !== undefined) {
        backendEnv.PAYPAL_ENABLED = updates.paypal.enabled.toString();
      }
      if (updates.paypal.mode !== undefined) {
        backendEnv.PAYPAL_MODE = updates.paypal.mode;
      }
      if (updates.paypal.currency !== undefined) {
        backendEnv.PAYPAL_CURRENCY = updates.paypal.currency;
      }
    }
    
    if (updates.tax) {
      if (updates.tax.enabled !== undefined) {
        backendEnv.TAX_ENABLED = updates.tax.enabled.toString();
      }
      if (updates.tax.rate !== undefined) {
        backendEnv.TAX_RATE = updates.tax.rate.toString();
      }
      if (updates.tax.includedInPrice !== undefined) {
        backendEnv.TAX_INCLUDED_IN_PRICE = updates.tax.includedInPrice.toString();
      }
    }
    
    if (updates.shipping) {
      if (updates.shipping.freeShippingThreshold !== undefined) {
        backendEnv.FREE_SHIPPING_THRESHOLD = updates.shipping.freeShippingThreshold.toString();
      }
      if (updates.shipping.flatRate !== undefined) {
        backendEnv.SHIPPING_FLAT_RATE = updates.shipping.flatRate.toString();
      }
      if (updates.shipping.expressRate !== undefined) {
        backendEnv.SHIPPING_EXPRESS_RATE = updates.shipping.expressRate.toString();
      }
    }
    
    await writeEnvFile(backendEnvPath, backendEnv);
    
    res.json({ success: true, message: 'Payment configuration updated successfully' });
  } catch (error) {
    console.error('Error updating payment config:', error);
    res.status(500).json({ success: false, error: 'Failed to update payment configuration' });
  }
};

// Get Voice configuration
export const getVoiceConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      stt: {
        provider: process.env.STT_PROVIDER || 'google',
        language: process.env.STT_LANGUAGE || 'en-US',
        enhancedModels: process.env.STT_ENHANCED_MODELS === 'true'
      },
      tts: {
        provider: process.env.TTS_PROVIDER || 'google',
        voice: process.env.TTS_VOICE || 'en-US-Wavenet-F',
        speed: parseFloat(process.env.TTS_SPEED || '1.0'),
        pitch: parseFloat(process.env.TTS_PITCH || '0')
      },
      processing: {
        maxAudioDuration: parseInt(process.env.MAX_AUDIO_DURATION || '300'),
        audioFormat: process.env.AUDIO_FORMAT || 'mp3',
        quality: process.env.AUDIO_QUALITY || 'high'
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching voice config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch voice configuration' });
  }
};

// Update Voice configuration
export const updateVoiceConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const voiceEnvPath = path.join(process.cwd(), '..', 'services', 'voice', '.env');
    const voiceEnv = await readEnvFile(voiceEnvPath);
    
    if (updates.stt) {
      if (updates.stt.provider !== undefined) {
        voiceEnv.STT_PROVIDER = updates.stt.provider;
      }
      if (updates.stt.language !== undefined) {
        voiceEnv.STT_LANGUAGE = updates.stt.language;
      }
      if (updates.stt.enhancedModels !== undefined) {
        voiceEnv.STT_ENHANCED_MODELS = updates.stt.enhancedModels.toString();
      }
    }
    
    if (updates.tts) {
      if (updates.tts.provider !== undefined) {
        voiceEnv.TTS_PROVIDER = updates.tts.provider;
      }
      if (updates.tts.voice !== undefined) {
        voiceEnv.TTS_VOICE = updates.tts.voice;
      }
      if (updates.tts.speed !== undefined) {
        voiceEnv.TTS_SPEED = updates.tts.speed.toString();
      }
      if (updates.tts.pitch !== undefined) {
        voiceEnv.TTS_PITCH = updates.tts.pitch.toString();
      }
    }
    
    if (updates.processing) {
      if (updates.processing.maxAudioDuration !== undefined) {
        voiceEnv.MAX_AUDIO_DURATION = updates.processing.maxAudioDuration.toString();
      }
      if (updates.processing.audioFormat !== undefined) {
        voiceEnv.AUDIO_FORMAT = updates.processing.audioFormat;
      }
      if (updates.processing.quality !== undefined) {
        voiceEnv.AUDIO_QUALITY = updates.processing.quality;
      }
    }
    
    await writeEnvFile(voiceEnvPath, voiceEnv);
    
    res.json({ success: true, message: 'Voice configuration updated successfully' });
  } catch (error) {
    console.error('Error updating voice config:', error);
    res.status(500).json({ success: false, error: 'Failed to update voice configuration' });
  }
};

// Get Email configuration
export const getEmailConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      provider: process.env.EMAIL_PROVIDER || 'sendgrid',
      fromEmail: process.env.EMAIL_FROM || '',
      fromName: process.env.EMAIL_FROM_NAME || 'Pelucas Chic',
      replyTo: process.env.EMAIL_REPLY_TO || '',
      templates: {
        orderConfirmation: process.env.EMAIL_TEMPLATE_ORDER_CONFIRMATION === 'true',
        orderShipped: process.env.EMAIL_TEMPLATE_ORDER_SHIPPED === 'true',
        passwordReset: process.env.EMAIL_TEMPLATE_PASSWORD_RESET === 'true',
        welcome: process.env.EMAIL_TEMPLATE_WELCOME === 'true'
      },
      marketing: {
        enabled: process.env.EMAIL_MARKETING_ENABLED === 'true',
        listId: process.env.EMAIL_MARKETING_LIST_ID || ''
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching email config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch email configuration' });
  }
};

// Update Email configuration
export const updateEmailConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const backendEnvPath = path.join(process.cwd(), '..', '.env');
    const backendEnv = await readEnvFile(backendEnvPath);
    
    if (updates.provider !== undefined) {
      backendEnv.EMAIL_PROVIDER = updates.provider;
    }
    if (updates.fromEmail !== undefined) {
      backendEnv.EMAIL_FROM = updates.fromEmail;
    }
    if (updates.fromName !== undefined) {
      backendEnv.EMAIL_FROM_NAME = updates.fromName;
    }
    if (updates.replyTo !== undefined) {
      backendEnv.EMAIL_REPLY_TO = updates.replyTo;
    }
    
    if (updates.templates) {
      if (updates.templates.orderConfirmation !== undefined) {
        backendEnv.EMAIL_TEMPLATE_ORDER_CONFIRMATION = updates.templates.orderConfirmation.toString();
      }
      if (updates.templates.orderShipped !== undefined) {
        backendEnv.EMAIL_TEMPLATE_ORDER_SHIPPED = updates.templates.orderShipped.toString();
      }
      if (updates.templates.passwordReset !== undefined) {
        backendEnv.EMAIL_TEMPLATE_PASSWORD_RESET = updates.templates.passwordReset.toString();
      }
      if (updates.templates.welcome !== undefined) {
        backendEnv.EMAIL_TEMPLATE_WELCOME = updates.templates.welcome.toString();
      }
    }
    
    if (updates.marketing) {
      if (updates.marketing.enabled !== undefined) {
        backendEnv.EMAIL_MARKETING_ENABLED = updates.marketing.enabled.toString();
      }
      if (updates.marketing.listId !== undefined) {
        backendEnv.EMAIL_MARKETING_LIST_ID = updates.marketing.listId;
      }
    }
    
    await writeEnvFile(backendEnvPath, backendEnv);
    
    res.json({ success: true, message: 'Email configuration updated successfully' });
  } catch (error) {
    console.error('Error updating email config:', error);
    res.status(500).json({ success: false, error: 'Failed to update email configuration' });
  }
};

// Get SEO configuration
export const getSEOConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      title: process.env.SEO_TITLE || 'Pelucas Chic Human Hair - Premium Wigs',
      description: process.env.SEO_DESCRIPTION || 'Shop premium quality human hair wigs...',
      keywords: process.env.SEO_KEYWORDS || 'wigs, human hair, lace front',
      ogImage: process.env.SEO_OG_IMAGE || '/images/og-image.jpg',
      twitterHandle: process.env.TWITTER_HANDLE || '@pelucas_chic',
      googleAnalytics: process.env.GOOGLE_ANALYTICS_ID || '',
      facebookPixel: process.env.FACEBOOK_PIXEL_ID || '',
      structuredData: {
        enabled: process.env.STRUCTURED_DATA_ENABLED !== 'false',
        businessType: process.env.BUSINESS_TYPE || 'Store',
        priceRange: process.env.PRICE_RANGE || '$$'
      }
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching SEO config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch SEO configuration' });
  }
};

// Update SEO configuration
export const updateSEOConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const frontendEnvPath = path.join(process.cwd(), '..', 'frontend', '.env.local');
    const frontendEnv = await readEnvFile(frontendEnvPath);
    
    if (updates.title !== undefined) {
      frontendEnv.SEO_TITLE = updates.title;
    }
    if (updates.description !== undefined) {
      frontendEnv.SEO_DESCRIPTION = updates.description;
    }
    if (updates.keywords !== undefined) {
      frontendEnv.SEO_KEYWORDS = updates.keywords;
    }
    if (updates.ogImage !== undefined) {
      frontendEnv.SEO_OG_IMAGE = updates.ogImage;
    }
    if (updates.twitterHandle !== undefined) {
      frontendEnv.TWITTER_HANDLE = updates.twitterHandle;
    }
    if (updates.googleAnalytics !== undefined) {
      frontendEnv.GOOGLE_ANALYTICS_ID = updates.googleAnalytics;
    }
    if (updates.facebookPixel !== undefined) {
      frontendEnv.FACEBOOK_PIXEL_ID = updates.facebookPixel;
    }
    
    if (updates.structuredData) {
      if (updates.structuredData.enabled !== undefined) {
        frontendEnv.STRUCTURED_DATA_ENABLED = updates.structuredData.enabled.toString();
      }
      if (updates.structuredData.businessType !== undefined) {
        frontendEnv.BUSINESS_TYPE = updates.structuredData.businessType;
      }
      if (updates.structuredData.priceRange !== undefined) {
        frontendEnv.PRICE_RANGE = updates.structuredData.priceRange;
      }
    }
    
    await writeEnvFile(frontendEnvPath, frontendEnv);
    
    res.json({ success: true, message: 'SEO configuration updated successfully' });
  } catch (error) {
    console.error('Error updating SEO config:', error);
    res.status(500).json({ success: false, error: 'Failed to update SEO configuration' });
  }
};

// Get Instagram configuration
export const getInstagramConfig = async (req: Request, res: Response) => {
  try {
    const config = {
      enabled: process.env.INSTAGRAM_ENABLED !== 'false',
      username: process.env.INSTAGRAM_USERNAME || '@pelucas_chic_human_hair',
      displayCount: parseInt(process.env.INSTAGRAM_DISPLAY_COUNT || '12'),
      refreshInterval: parseInt(process.env.INSTAGRAM_REFRESH_INTERVAL || '3600'),
      showOnHomepage: process.env.INSTAGRAM_SHOW_ON_HOMEPAGE !== 'false',
      showOnProductPages: process.env.INSTAGRAM_SHOW_ON_PRODUCT_PAGES === 'true'
    };
    
    res.json({ success: true, config });
  } catch (error) {
    console.error('Error fetching Instagram config:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch Instagram configuration' });
  }
};

// Update Instagram configuration
export const updateInstagramConfig = async (req: Request, res: Response) => {
  try {
    const updates = req.body;
    
    const frontendEnvPath = path.join(process.cwd(), '..', 'frontend', '.env.local');
    const frontendEnv = await readEnvFile(frontendEnvPath);
    
    if (updates.enabled !== undefined) {
      frontendEnv.INSTAGRAM_ENABLED = updates.enabled.toString();
    }
    if (updates.username !== undefined) {
      frontendEnv.INSTAGRAM_USERNAME = updates.username;
    }
    if (updates.displayCount !== undefined) {
      frontendEnv.INSTAGRAM_DISPLAY_COUNT = updates.displayCount.toString();
    }
    if (updates.refreshInterval !== undefined) {
      frontendEnv.INSTAGRAM_REFRESH_INTERVAL = updates.refreshInterval.toString();
    }
    if (updates.showOnHomepage !== undefined) {
      frontendEnv.INSTAGRAM_SHOW_ON_HOMEPAGE = updates.showOnHomepage.toString();
    }
    if (updates.showOnProductPages !== undefined) {
      frontendEnv.INSTAGRAM_SHOW_ON_PRODUCT_PAGES = updates.showOnProductPages.toString();
    }
    
    await writeEnvFile(frontendEnvPath, frontendEnv);
    
    res.json({ success: true, message: 'Instagram configuration updated successfully' });
  } catch (error) {
    console.error('Error updating Instagram config:', error);
    res.status(500).json({ success: false, error: 'Failed to update Instagram configuration' });
  }
};

// Export all configuration
export const exportConfig = async (req: Request, res: Response) => {
  try {
    const configs = {
      apiKeys: await getApiKeys(req, res),
      system: await getSystemConfig(req, res),
      whatsapp: await getWhatsAppConfig(req, res),
      chatbot: await getChatbotConfig(req, res),
      payment: await getPaymentConfig(req, res),
      voice: await getVoiceConfig(req, res),
      email: await getEmailConfig(req, res),
      seo: await getSEOConfig(req, res),
      instagram: await getInstagramConfig(req, res),
      exportDate: new Date().toISOString()
    };
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=pelucas-chic-config-export.json');
    res.json(configs);
  } catch (error) {
    console.error('Error exporting config:', error);
    res.status(500).json({ success: false, error: 'Failed to export configuration' });
  }
};

// Import configuration
export const importConfig = async (req: Request, res: Response) => {
  try {
    const { config } = req.body;
    
    if (!config || typeof config !== 'object') {
      return res.status(400).json({ success: false, error: 'Invalid configuration data' });
    }
    
    // TODO: Implement configuration import with validation
    // This would involve parsing the imported config and updating all relevant env files
    
    res.json({ success: true, message: 'Configuration import not yet implemented' });
  } catch (error) {
    console.error('Error importing config:', error);
    res.status(500).json({ success: false, error: 'Failed to import configuration' });
  }
};

// Get system health
export const getSystemHealth = async (req: Request, res: Response) => {
  try {
    const health = {
      database: {
        status: 'unknown',
        message: ''
      },
      apis: {
        aliexpress: { status: 'unknown', message: '' },
        stripe: { status: 'unknown', message: '' },
        paypal: { status: 'unknown', message: '' },
        whatsapp: { status: 'unknown', message: '' }
      },
      services: {
        backend: { status: 'healthy', uptime: process.uptime() },
        frontend: { status: 'unknown' },
        chatbot: { status: 'unknown' },
        whatsapp: { status: 'unknown' }
      }
    };
    
    // Check database connection
    try {
      const dbStatus = await User.findOne().limit(1);
      health.database.status = 'healthy';
      health.database.message = 'Connected';
    } catch (error) {
      health.database.status = 'unhealthy';
      health.database.message = 'Connection failed';
    }
    
    // Check API connections (simplified - in production, implement proper health checks)
    health.apis.aliexpress.status = process.env.ALIEXPRESS_APP_KEY ? 'configured' : 'not_configured';
    health.apis.stripe.status = process.env.STRIPE_SECRET_KEY ? 'configured' : 'not_configured';
    health.apis.paypal.status = process.env.PAYPAL_CLIENT_ID ? 'configured' : 'not_configured';
    health.apis.whatsapp.status = process.env.WHATSAPP_ACCESS_TOKEN ? 'configured' : 'not_configured';
    
    res.json({ success: true, health });
  } catch (error) {
    console.error('Error checking system health:', error);
    res.status(500).json({ success: false, error: 'Failed to check system health' });
  }
};

// Get API usage statistics
export const getAPIUsageStats = async (req: Request, res: Response) => {
  try {
    // This would typically fetch from a metrics database or monitoring service
    const stats = {
      aliexpress: {
        dailyCalls: 0,
        monthlyTotal: 0,
        limit: 10000,
        lastSync: null
      },
      openai: {
        tokensUsedToday: 0,
        tokensUsedThisMonth: 0,
        estimatedCost: 0
      },
      whatsapp: {
        messagesReceivedToday: 0,
        messagesSentToday: 0,
        monthlyTotal: 0
      },
      voiceProcessing: {
        sttMinutesToday: 0,
        ttsCharactersToday: 0,
        monthlySTTMinutes: 0,
        monthlyTTSCharacters: 0
      },
      payments: {
        stripeTransactionsToday: 0,
        paypalTransactionsToday: 0,
        totalRevenuToday: 0
      }
    };
    
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Error fetching API usage stats:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch API usage statistics' });
  }
};

// Sync all products to Stripe
export const syncProductsToStripe = async (req: Request, res: Response) => {
  try {
    console.log('Starting Stripe product sync...');

    const result = await stripeProductSyncService.syncAllProducts();

    console.log(`Stripe sync completed: ${result.synced} synced, ${result.errors} errors`);

    res.json({
      success: true,
      message: `Successfully synced ${result.synced} products to Stripe`,
      stats: result
    });
  } catch (error) {
    console.error('Error syncing products to Stripe:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync products to Stripe'
    });
  }
};