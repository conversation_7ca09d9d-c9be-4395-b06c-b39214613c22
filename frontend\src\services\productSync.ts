import { AliExpressCatalogService, AliExpressProduct, LocalProduct } from './aliexpressCatalog';
import { translationService } from './translationService';
import { Locale } from '@/i18n/config';

// Extended StoreProduct interface for our internal use
export interface StoreProduct {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  discount: string;
  imageUrl: string;
  images: string[];
  category: string;
  subcategory: string;
  description: string;
  features: string[];
  rating: number;
  reviews: number;
  sales: number;
  storeId: string;
  storeName: string;
  inStock: boolean;
  tags: string[];
  sku: string;
  slug: string;
  // Multilingual support
  translations?: {
    [locale: string]: {
      title: string;
      description: string;
      category: string;
      subcategory: string;
      features: string[];
      tags: string[];
    };
  };
}

class ProductSyncService {
  private products: StoreProduct[] = [];
  private lastSync: Date | null = null;
  private isLoading = false;

  /**
   * Sync ALL products from both AliExpress stores
   */
  async syncAllStoreProducts(): Promise<void> {
    if (this.isLoading) return;
    
    this.isLoading = true;
    console.log('🔄 Starting full product sync from both stores...');

    try {
      const storeIds = ['3497011', '4758009'];
      const allProducts: StoreProduct[] = [];

      for (const storeId of storeIds) {
        console.log(`📦 Syncing products from store ${storeId}...`);
        
        // Try to get products from API with pagination
        let page = 1;
        let hasMoreProducts = true;
        
        while (hasMoreProducts && page <= 10) { // Limit to 10 pages per store
          try {
            const response = await AliExpressCatalogService.getStoreProducts(storeId, page, 50);
            
            if (response.success && response.data?.products && response.data.products.length > 0) {
              const convertedProducts = this.convertApiProductsToStoreProducts(
                response.data.products, 
                storeId
              );
              allProducts.push(...convertedProducts);
              console.log(`📄 Page ${page}: Found ${convertedProducts.length} products`);
              
              // Check if there are more pages
              hasMoreProducts = response.data.products.length === 50;
              page++;
            } else {
              console.log(`⚠️ Store ${storeId} API returned no products. Will continue with empty result.`);
              hasMoreProducts = false;
            }
          } catch (error) {
            console.error(`❌ Error syncing store ${storeId}:`, error);
            hasMoreProducts = false;
          }
        }
      }

      this.products = allProducts;
      this.lastSync = new Date();
      
      console.log(`✅ Product sync complete! Total products: ${this.products.length}`);
      console.log(`📊 Store 3497011: ${this.products.filter(p => p.storeId === '3497011').length} products`);
      console.log(`📊 Store 4758009: ${this.products.filter(p => p.storeId === '4758009').length} products`);
      
    } catch (error) {
      console.error('❌ Product sync failed:', error);
      // Initialize with empty array instead of fallback products
      this.products = [];
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Get all products from cache (sync if needed)
   */
  async getAllProducts(): Promise<StoreProduct[]> {
    // Sync if we don't have products or it's been more than 1 hour
    const shouldSync = !this.products.length ||
                      !this.lastSync ||
                      (Date.now() - this.lastSync.getTime()) > 3600000;

    if (shouldSync) {
      await this.syncAllStoreProducts();
    }

    return this.products;
  }

  /**
   * Get products with translations for a specific locale
   */
  async getProductsWithTranslations(locale: Locale = 'en'): Promise<StoreProduct[]> {
    const products = await this.getAllProducts();

    if (locale === 'en') {
      return products; // English is the default, no translation needed
    }

    // Check if products need translation
    const productsNeedingTranslation = products.filter(product =>
      !product.translations || !product.translations[locale]
    );

    if (productsNeedingTranslation.length > 0) {
      console.log(`🌍 Translating ${productsNeedingTranslation.length} products to ${locale}...`);
      await this.translateProducts(productsNeedingTranslation, locale);
    }

    return products.map(product => {
      if (product.translations && product.translations[locale]) {
        return {
          ...product,
          title: product.translations[locale].title,
          description: product.translations[locale].description,
          category: product.translations[locale].category,
          subcategory: product.translations[locale].subcategory,
          features: product.translations[locale].features,
          tags: product.translations[locale].tags,
        };
      }
      return product;
    });
  }

  /**
   * Translate products to a specific locale
   */
  private async translateProducts(products: StoreProduct[], locale: Locale): Promise<void> {
    const batchSize = 5; // Process in small batches to avoid rate limits

    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);

      await Promise.all(batch.map(async (product) => {
        try {
          const [
            translatedTitle,
            translatedDescription,
            translatedCategory,
            translatedSubcategory,
            translatedFeatures,
            translatedTags
          ] = await Promise.all([
            translationService.translate(product.title, locale, 'auto', 'product'),
            translationService.translate(product.description, locale, 'auto', 'description'),
            translationService.translate(product.category, locale, 'auto', 'category'),
            translationService.translate(product.subcategory, locale, 'auto', 'category'),
            Promise.all(product.features.map(feature =>
              translationService.translate(feature, locale, 'auto', 'general')
            )),
            Promise.all(product.tags.map(tag =>
              translationService.translate(tag, locale, 'auto', 'general')
            ))
          ]);

          // Initialize translations object if it doesn't exist
          if (!product.translations) {
            product.translations = {};
          }

          // Store translations
          product.translations[locale] = {
            title: translatedTitle,
            description: translatedDescription,
            category: translatedCategory,
            subcategory: translatedSubcategory,
            features: translatedFeatures,
            tags: translatedTags
          };

          console.log(`✅ Translated product: ${product.title} -> ${translatedTitle}`);
        } catch (error) {
          console.error(`❌ Failed to translate product ${product.id}:`, error);
        }
      }));

      // Small delay between batches
      if (i + batchSize < products.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(category: string): Promise<StoreProduct[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter(product => 
      product.category.toLowerCase().includes(category.toLowerCase()) ||
      product.subcategory.toLowerCase().includes(category.toLowerCase())
    );
  }

  /**
   * Get products by store
   */
  async getProductsByStore(storeId: string): Promise<StoreProduct[]> {
    const allProducts = await this.getAllProducts();
    return allProducts.filter(product => product.storeId === storeId);
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit: number = 12): Promise<StoreProduct[]> {
    const allProducts = await this.getAllProducts();
    return allProducts
      .sort((a, b) => b.rating - a.rating || b.sales - a.sales)
      .slice(0, limit);
  }

  /**
   * Search products
   */
  async searchProducts(query: string, limit: number = 20): Promise<StoreProduct[]> {
    const allProducts = await this.getAllProducts();
    const searchTerm = query.toLowerCase();
    
    return allProducts
      .filter(product =>
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        product.category.toLowerCase().includes(searchTerm)
      )
      .slice(0, limit);
  }

  /**
   * Helper methods for product data processing
   */
  private getProductCategory(productTitle: string): string {
    const title = productTitle?.toLowerCase() || '';
    if (title.includes('lace front')) return 'Lace Front Wigs';
    if (title.includes('bob')) return 'Bob Wigs';
    if (title.includes('full lace')) return 'Full Lace Wigs';
    if (title.includes('360')) return 'Lace Front Wigs';
    if (title.includes('closure')) return 'Closure Wigs';
    if (title.includes('frontal')) return 'Lace Front Wigs';
    return 'Hair Wigs';
  }

  private getProductSubcategory(productTitle: string): string {
    const title = productTitle?.toLowerCase() || '';
    if (title.includes('brazilian')) return 'Brazilian Hair';
    if (title.includes('peruvian')) return 'Peruvian Hair';
    if (title.includes('malaysian')) return 'Malaysian Hair';
    if (title.includes('indian')) return 'Indian Hair';
    if (title.includes('synthetic')) return 'Synthetic Hair';
    if (title.includes('human')) return 'Human Hair';
    if (title.includes('remy')) return 'Remy Hair';
    if (title.includes('virgin')) return 'Virgin Hair';
    return 'Human Hair';
  }

  /**
   * Convert API products to our format
   */
  /**
   * Convert AliExpress API products to our internal StoreProduct format
   * Leverages the AliExpressProduct type for proper type checking
   */
  private convertApiProductsToStoreProducts(apiProducts: AliExpressProduct[], storeId: string): StoreProduct[] {
    return apiProducts.map(product => {
      // Calculate discount percentage if available
      const price = parseFloat(product.target_sale_price) || 0;
      const originalPrice = product.original_price ? 
        parseFloat(product.original_price) : 
        (product.target_original_price ? parseFloat(product.target_original_price) : price * 1.25);
      
      // Calculate discount percentage
      const discountValue = originalPrice > 0 ? ((1 - (price / originalPrice)) * 100) : 0;
      const discount = product.discount || `${discountValue.toFixed(0)}%`;
      
      // Extract or generate tags from product title
      const tags = product.product_title?.toLowerCase()
        .split(' ')
        .filter(word => word.length > 3) || [];
      
      return {
        id: product.product_id,
        title: product.product_title,
        price: price,
        originalPrice: originalPrice,
        discount,
        imageUrl: product.product_main_image_url,
        images: product.product_small_image_urls || [],
        category: this.getProductCategory(product.product_title),
        subcategory: this.getProductSubcategory(product.product_title),
        description: product.product_title,
        features: ['Premium Quality', 'Natural Look', 'Comfortable Fit'],
        rating: parseFloat(product.evaluate_rate || '4.5'),
        reviews: parseInt(product.volume || '0'),
        sales: parseInt(product.volume || '0'),
        storeId,
        storeName: product.shop_name || (storeId === '3497011' ? 'Premium Hair Collection' : 'Elite Beauty Store'),
        inStock: true,
        tags,
        sku: `${storeId}-${product.product_id}`,
        slug: product.product_id ? 
          `${product.product_title?.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${product.product_id}` : 
          `product-${product.product_id}`
      };
    });
  }
}

// Export singleton instance
export const productSyncService = new ProductSyncService();
export default productSyncService;