'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface FilterPreset {
  id: string;
  name: string;
  description: string;
  icon: string; // Now refers to icon component name
  filters: {
    priceRange?: [number, number];
    colors?: string[];
    lengths?: string[];
    styles?: string[];
    density?: string[];
    capConstruction?: string[];
    material?: string[];
  };
}

interface FilterPresetsProps {
  onApplyPreset: (filters: any) => void;
  className?: string;
}

// Icon components for better performance and customization
const IconComponents = {
  DollarSign: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
    </svg>
  ),
  Sparkles: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l1.5 1.5L5 6l1.5 1.5L5 9l1.5 1.5L5 12l1.5 1.5L5 15l1.5 1.5L5 18l1.5 1.5L5 21M19 3l-1.5 1.5L19 6l-1.5 1.5L19 9l-1.5 1.5L19 12l-1.5 1.5L19 15l-1.5 1.5L19 18l-1.5 1.5L19 21" />
    </svg>
  ),
  Leaf: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
    </svg>
  ),
  Sun: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
    </svg>
  ),
  Scissors: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
    </svg>
  ),
  Crown: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l2 9h10l2-9M5 3l2 2 2-2 2 2 2-2 2 2 2-2v0M7 21h10" />
    </svg>
  ),
  Mask: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
  ),
  Star: () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
    </svg>
  )
};

const presets: FilterPreset[] = [
  {
    id: 'budget-friendly',
    name: 'Budget Friendly',
    description: 'Quality wigs under $200',
    icon: 'DollarSign',
    filters: {
      priceRange: [0, 200],
      material: ['synthetic', 'heat-friendly-synthetic']
    }
  },
  {
    id: 'premium-human',
    name: 'Premium Human Hair',
    description: 'Luxury human hair wigs',
    icon: 'Sparkles',
    filters: {
      priceRange: [300, 1000],
      material: ['human-remy', 'human-virgin']
    }
  },
  {
    id: 'natural-colors',
    name: 'Natural Colors',
    description: 'Classic natural hair colors',
    icon: 'Leaf',
    filters: {
      colors: ['black', 'darkBrown', 'mediumBrown', 'lightBrown']
    }
  },
  {
    id: 'blonde-collection',
    name: 'Blonde Collection',
    description: 'All shades of blonde',
    icon: 'Sun',
    filters: {
      colors: ['blonde', 'platinum']
    }
  },
  {
    id: 'short-styles',
    name: 'Short & Chic',
    description: 'Short and bob styles',
    icon: 'Scissors',
    filters: {
      lengths: ['short', 'bob'],
      styles: ['bob', 'pixie', 'short-layered']
    }
  },
  {
    id: 'long-glamour',
    name: 'Long & Glamorous',
    description: 'Long flowing styles',
    icon: 'Crown',
    filters: {
      lengths: ['long', 'extra-long'],
      styles: ['straight', 'wavy', 'curly']
    }
  },
  {
    id: 'lace-front',
    name: 'Lace Front',
    description: 'Natural hairline wigs',
    icon: 'Mask',
    filters: {
      capConstruction: ['lace-front', 'full-lace']
    }
  },
  {
    id: 'beginner-friendly',
    name: 'Beginner Friendly',
    description: 'Easy to wear and style',
    icon: 'Star',
    filters: {
      capConstruction: ['basic-cap', 'monofilament'],
      material: ['synthetic', 'heat-friendly-synthetic'],
      priceRange: [50, 300]
    }
  }
];

const FilterPresets: React.FC<FilterPresetsProps> = ({ onApplyPreset, className = '' }) => {
  const renderIcon = (iconName: string) => {
    const IconComponent = IconComponents[iconName as keyof typeof IconComponents];
    return IconComponent ? <IconComponent /> : null;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Quick Filters</h3>
        <span className="text-sm text-gray-500">Popular combinations</span>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {presets.map((preset, index) => (
          <motion.button
            key={preset.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => onApplyPreset(preset.filters)}
            className="group relative bg-white border border-gray-200 rounded-xl p-4 hover:border-pink-300 hover:shadow-lg transition-all duration-300 text-left overflow-hidden"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Background gradient on hover */}
            <div className="absolute inset-0 bg-gradient-to-br from-pink-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Icon */}
            <div className="relative z-10 w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center text-white mb-3 group-hover:scale-110 transition-transform duration-300">
              {renderIcon(preset.icon)}
            </div>

            {/* Content */}
            <div className="relative z-10">
              <h4 className="font-semibold text-gray-900 group-hover:text-pink-600 transition-colors duration-300 text-sm">
                {preset.name}
              </h4>
              <p className="text-xs text-gray-600 mt-1 line-clamp-2 group-hover:text-gray-700 transition-colors duration-300">
                {preset.description}
              </p>
            </div>

            {/* Subtle border glow on hover */}
            <div className="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-pink-200 transition-colors duration-300" />
          </motion.button>
        ))}
      </div>

      {/* Custom filter hint */}
      <div className="text-center pt-6 border-t border-gray-200">
        <p className="text-sm text-gray-500">
          Can't find what you're looking for? Use the detailed filters below to create your perfect search.
        </p>
      </div>
    </div>
  );
};

export default FilterPresets;
