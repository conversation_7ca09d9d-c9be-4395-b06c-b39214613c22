'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  GlobeAltIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface SEOConfig {
  enabled: boolean;
  general: {
    siteTitle: string;
    siteDescription: string;
    keywords: string[];
    canonicalUrl: string;
    robotsTxt: string;
  };
  analytics: {
    googleAnalytics: {
      enabled: boolean;
      trackingId: string;
    };
    googleTagManager: {
      enabled: boolean;
      containerId: string;
    };
    facebookPixel: {
      enabled: boolean;
      pixelId: string;
    };
  };
  schema: {
    enabled: boolean;
    businessType: string;
    businessName: string;
    address: string;
    phone: string;
    email: string;
    socialProfiles: string[];
  };
  sitemap: {
    enabled: boolean;
    autoGenerate: boolean;
    includeImages: boolean;
    changeFreq: string;
    priority: number;
  };
  openGraph: {
    enabled: boolean;
    defaultImage: string;
    twitterCard: string;
    facebookAppId: string;
  };
}

export default function SEOConfigPage() {
  const [config, setConfig] = useState<SEOConfig>({
    enabled: true,
    general: {
      siteTitle: 'Pelucas Chic - Premium Human Hair Wigs',
      siteDescription: 'Discover our collection of premium human hair wigs. High-quality, natural-looking wigs for every style and occasion.',
      keywords: ['human hair wigs', 'premium wigs', 'natural wigs', 'hair extensions', 'beauty'],
      canonicalUrl: 'https://pelucaschic.com',
      robotsTxt: 'User-agent: *\nAllow: /'
    },
    analytics: {
      googleAnalytics: {
        enabled: false,
        trackingId: ''
      },
      googleTagManager: {
        enabled: false,
        containerId: ''
      },
      facebookPixel: {
        enabled: false,
        pixelId: ''
      }
    },
    schema: {
      enabled: true,
      businessType: 'Store',
      businessName: 'Pelucas Chic',
      address: '',
      phone: '',
      email: '<EMAIL>',
      socialProfiles: []
    },
    sitemap: {
      enabled: true,
      autoGenerate: true,
      includeImages: true,
      changeFreq: 'weekly',
      priority: 0.8
    },
    openGraph: {
      enabled: true,
      defaultImage: '/images/og-default.jpg',
      twitterCard: 'summary_large_image',
      facebookAppId: ''
    }
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  const businessTypes = [
    'Store',
    'OnlineStore',
    'LocalBusiness',
    'Organization',
    'Corporation'
  ];

  const changeFreqOptions = [
    'always',
    'hourly',
    'daily',
    'weekly',
    'monthly',
    'yearly',
    'never'
  ];

  const twitterCardTypes = [
    'summary',
    'summary_large_image',
    'app',
    'player'
  ];

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/admin/config/seo', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching SEO config:', error);
      toast.error('Failed to load SEO configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      const response = await fetch('/api/admin/config/seo', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(config)
      });
      
      if (response.ok) {
        toast.success('SEO configuration updated successfully');
      } else {
        throw new Error('Failed to update configuration');
      }
    } catch (error) {
      console.error('Error updating SEO config:', error);
      toast.error('Failed to update SEO configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    setTesting(true);
    
    try {
      const response = await fetch('/api/admin/seo/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success('SEO configuration is valid');
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error testing SEO:', error);
      toast.error('Failed to test SEO configuration');
    } finally {
      setTesting(false);
    }
  };

  const addKeyword = () => {
    setConfig(prev => ({
      ...prev,
      general: {
        ...prev.general,
        keywords: [...prev.general.keywords, '']
      }
    }));
  };

  const updateKeyword = (index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      general: {
        ...prev.general,
        keywords: prev.general.keywords.map((k, i) => i === index ? value : k)
      }
    }));
  };

  const removeKeyword = (index: number) => {
    setConfig(prev => ({
      ...prev,
      general: {
        ...prev.general,
        keywords: prev.general.keywords.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">SEO Configuration</h1>
          <p className="text-gray-600 mt-1">
            Configure search engine optimization settings and analytics
          </p>
        </div>
        <button
          onClick={handleTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {testing ? (
            <>
              <ArrowPathIcon className="animate-spin h-4 w-4 mr-2" />
              Testing...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Test SEO
            </>
          )}
        </button>
      </div>

      {/* General SEO Settings */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <GlobeAltIcon className="h-5 w-5 mr-2" />
            General SEO Settings
          </h2>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Enable/Disable */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Enable SEO Features</h3>
              <p className="text-sm text-gray-500">
                Turn on/off SEO optimization features
              </p>
            </div>
            <button
              onClick={() => setConfig(prev => ({ ...prev, enabled: !prev.enabled }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="grid grid-cols-1 gap-6">
            {/* Site Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Title
              </label>
              <input
                type="text"
                value={config.general.siteTitle}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  general: { ...prev.general, siteTitle: e.target.value }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Your site title"
              />
              <p className="mt-1 text-sm text-gray-500">
                Appears in search results and browser tabs (50-60 characters recommended)
              </p>
            </div>

            {/* Site Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Description
              </label>
              <textarea
                value={config.general.siteDescription}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  general: { ...prev.general, siteDescription: e.target.value }
                }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                placeholder="Brief description of your website"
              />
              <p className="mt-1 text-sm text-gray-500">
                Meta description for search results (150-160 characters recommended)
              </p>
            </div>

            {/* Keywords */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <TagIcon className="inline h-4 w-4 mr-1" />
                Keywords
              </label>
              <div className="space-y-2">
                {config.general.keywords.map((keyword, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="text"
                      value={keyword}
                      onChange={(e) => updateKeyword(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                      placeholder="Enter keyword"
                    />
                    <button
                      onClick={() => removeKeyword(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  onClick={addKeyword}
                  className="text-sm text-primary hover:text-primary/80"
                >
                  + Add Keyword
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-5 w-5 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>
    </div>
  );
}
