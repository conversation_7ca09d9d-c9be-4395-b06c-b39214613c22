'use client';

import React, { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  hover3D?: boolean;
  glowEffect?: boolean;
  borderGradient?: boolean;
  floatingElements?: boolean;
  clickable?: boolean;
  onClick?: () => void;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className = '',
  hover3D = false,
  glowEffect = false,
  borderGradient = false,
  floatingElements = false,
  clickable = false,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);

  const rotateX = useTransform(mouseYSpring, [-0.5, 0.5], ['7.5deg', '-7.5deg']);
  const rotateY = useTransform(mouseXSpring, [-0.5, 0.5], ['-7.5deg', '7.5deg']);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current || !hover3D) return;

    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const xPct = mouseX / width - 0.5;
    const yPct = mouseY / height - 0.5;

    x.set(xPct);
    y.set(yPct);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
    setIsHovered(false);
  };

  const cardStyle = hover3D ? {
    rotateY: rotateY,
    rotateX: rotateX,
    transformStyle: 'preserve-3d' as const,
  } : {};

  return (
    <motion.div
      ref={ref}
      className={`relative ${clickable ? 'cursor-pointer' : ''} ${className}`}
      style={cardStyle}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      onClick={clickable ? onClick : undefined}
      whileHover={hover3D ? { scale: 1.02 } : { scale: 1.01, y: -2 }}
      whileTap={clickable ? { scale: 0.98 } : {}}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      {/* Glow Effect */}
      {glowEffect && (
        <motion.div
          className="absolute -inset-1 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-blue-500/20 rounded-2xl blur-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Border Gradient */}
      {borderGradient && (
        <motion.div
          className="absolute inset-0 rounded-2xl p-[1px] bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="w-full h-full bg-white rounded-2xl" />
        </motion.div>
      )}

      {/* Main Card */}
      <motion.div
        className="relative bg-white rounded-2xl shadow-lg overflow-hidden"
        style={hover3D ? {
          transform: 'translateZ(50px)',
          transformStyle: 'preserve-3d',
        } : {}}
        animate={{
          boxShadow: isHovered
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            : '0 10px 25px -3px rgba(0, 0, 0, 0.1)'
        }}
        transition={{ duration: 0.3 }}
      >
        {/* Floating Elements */}
        {floatingElements && (
          <>
            <motion.div
              className="absolute top-4 right-4 w-2 h-2 bg-pink-400 rounded-full"
              style={hover3D ? { transform: 'translateZ(75px)' } : {}}
              animate={{
                scale: isHovered ? [1, 1.3, 1] : 1,
                opacity: isHovered ? [0.5, 1, 0.5] : 0.5,
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            
            <motion.div
              className="absolute bottom-4 left-4 w-3 h-3 bg-purple-400 rounded-full"
              style={hover3D ? { transform: 'translateZ(60px)' } : {}}
              animate={{
                scale: isHovered ? [1, 1.2, 1] : 1,
                opacity: isHovered ? [0.3, 0.8, 0.3] : 0.3,
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.5,
              }}
            />

            <motion.div
              className="absolute top-1/2 left-2 w-1 h-1 bg-blue-400 rounded-full"
              style={hover3D ? { transform: 'translateZ(40px)' } : {}}
              animate={{
                scale: isHovered ? [1, 1.5, 1] : 1,
                opacity: isHovered ? [0.4, 0.9, 0.4] : 0.4,
              }}
              transition={{
                duration: 1.8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }}
            />
          </>
        )}

        {/* Content */}
        <motion.div
          style={hover3D ? {
            transform: 'translateZ(25px)',
            transformStyle: 'preserve-3d',
          } : {}}
          animate={{
            y: isHovered ? -2 : 0,
          }}
          transition={{ duration: 0.3 }}
        >
          {children}
        </motion.div>

        {/* Hover Overlay */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-purple-500/5 to-blue-500/5"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>
    </motion.div>
  );
};

export default ModernCard;
