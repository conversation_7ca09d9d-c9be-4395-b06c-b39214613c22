'use client';

import React, { useEffect, useRef } from 'react';
import { motion, useInView, useAnimation } from 'framer-motion';

interface SplitTextProps {
  children: string;
  className?: string;
  delay?: number;
  duration?: number;
  staggerDelay?: number;
  variant?: 'fadeUp' | 'fadeIn' | 'slideIn' | 'scale' | 'rotate';
}

const SplitText: React.FC<SplitTextProps> = ({
  children,
  className = '',
  delay = 0,
  duration = 0.6,
  staggerDelay = 0.05,
  variant = 'fadeUp'
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-100px' });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  const variants = {
    fadeUp: {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0 }
    },
    fadeIn: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 }
    },
    slideIn: {
      hidden: { opacity: 0, x: -20 },
      visible: { opacity: 1, x: 0 }
    },
    scale: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 }
    },
    rotate: {
      hidden: { opacity: 0, rotateX: -90 },
      visible: { opacity: 1, rotateX: 0 }
    }
  };

  const words = children.split(' ');

  return (
    <div ref={ref} className={className}>
      {words.map((word, wordIndex) => (
        <span key={wordIndex} className="inline-block overflow-hidden">
          {word.split('').map((char, charIndex) => (
            <motion.span
              key={charIndex}
              className="inline-block"
              variants={variants[variant]}
              initial="hidden"
              animate={controls}
              transition={{
                duration,
                delay: delay + (wordIndex * words.length + charIndex) * staggerDelay,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
            >
              {char}
            </motion.span>
          ))}
          {wordIndex < words.length - 1 && (
            <motion.span
              className="inline-block"
              variants={variants[variant]}
              initial="hidden"
              animate={controls}
              transition={{
                duration,
                delay: delay + (wordIndex * words.length + word.length) * staggerDelay,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
            >
              &nbsp;
            </motion.span>
          )}
        </span>
      ))}
    </div>
  );
};

export default SplitText;
