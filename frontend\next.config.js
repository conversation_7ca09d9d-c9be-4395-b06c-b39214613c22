/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Production optimization
  swcMinify: true,
  
  // For production deployment - use standalone for API routes
  output: 'standalone', // This should generate standalone output directory in Next.js 14.2.29
  trailingSlash: true,
  
  // Image optimization
  images: {
    unoptimized: process.env.NODE_ENV === 'production', // Required for static export
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'ae01.alicdn.com',
      },
      {
        protocol: 'https',
        hostname: 'ae04.alicdn.com',
      },
    ],
  },
  
  // Environment variables for client-side
  env: {
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    ALIEXPRESS_APP_KEY: process.env.ALIEXPRESS_APP_KEY,
  },
  
  // Redirects
  async redirects() {
    return [
      // Redirect legacy product URLs to the new format
      {
        source: '/product-:id(\\d+)',
        destination: '/product/product-:id',
        permanent: true,
      },
      // Redirect URLs without trailing slashes to ones with trailing slashes
      // This helps with 404 errors for shop category pages
      {
        source: '/shop/lace-front',
        destination: '/shop/lace-front/',
        permanent: true,
      },
      {
        source: '/shop/bob',
        destination: '/shop/bob/',
        permanent: true,
      },
      {
        source: '/shop/full-lace',
        destination: '/shop/full-lace/',
        permanent: true,
      },
      // Generic redirect for any path that doesn't have a trailing slash
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'pelucaschichumanhair.com',
          },
        ],
        destination: '/:path*/',
        permanent: true,
      },
    ]
  },
  
  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          // Fix Content Security Policy to handle eval blocking
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://ajax.googleapis.com https://cdn.jsdelivr.net https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; img-src 'self' data: https: http:; connect-src 'self' https://api.aliexpress.com https://www.google-analytics.com;",
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.CORS_ORIGIN || '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },
};

module.exports = nextConfig;
