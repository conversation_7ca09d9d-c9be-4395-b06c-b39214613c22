'use client';

import React from 'react';
import { motion } from 'framer-motion';
import ProfileCard from './ProfileCard';
import SplitText from '../animations/SplitText';
import RevealText from '../animations/RevealText';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  image: string;
  description: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    instagram?: string;
  };
}

interface TeamSectionProps {
  title?: string;
  subtitle?: string;
  members?: TeamMember[];
  className?: string;
}

const defaultMembers: TeamMember[] = [
  {
    id: '2',
    name: '<PERSON>',
    role: 'Head of Design',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    description: '<PERSON> leads our design team, ensuring every wig meets our high standards for style and comfort.',
    social: {
      instagram: '#',
      twitter: '#'
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Quality Specialist',
    image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
    description: '<PERSON> oversees quality control, making sure every product exceeds customer expectations.',
    social: {
      linkedin: '#'
    }
  },
  {
    id: '4',
    name: 'Isabella Garcia',
    role: 'Customer Success',
    image: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=400&fit=crop&crop=face',
    description: 'Isabella ensures our customers have the best experience from consultation to after-sales support.',
    social: {
      instagram: '#',
      twitter: '#'
    }
  }
];

const TeamSection: React.FC<TeamSectionProps> = ({
  title = 'Meet Our Team',
  subtitle = 'The passionate professionals behind Pelucas Chic',
  members = defaultMembers,
  className = ''
}) => {
  return (
    <section className={`relative py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 overflow-hidden ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <RevealText delay={0.2}>
            <span className="text-primary font-semibold text-lg tracking-wide uppercase">
              Our Team
            </span>
          </RevealText>
          
          <SplitText 
            className="text-4xl md:text-5xl font-bold text-gray-900 mt-4 mb-6"
            delay={0.5}
            variant="fadeUp"
          >
            {title}
          </SplitText>
          
          <RevealText delay={1} direction="up">
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {subtitle}
            </p>
          </RevealText>
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12 max-w-5xl mx-auto">
          {members.map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: '-100px' }}
              transition={{
                duration: 0.6,
                delay: index * 0.2,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
            >
              <ProfileCard
                avatarUrl={member.image}
                name={member.name}
                title={member.role}
                description={member.description}
                handle={member.name.toLowerCase().replace(' ', '')}
                status="Available"
                contactText="Contact"
                showUserInfo={true}
                enableTilt={true}
                className="h-full"
                onContactClick={() => {
                  // Handle contact click - could open modal, navigate to contact page, etc.
                  console.log(`Contact ${member.name}`);
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <RevealText delay={1.5} direction="up">
          <div className="text-center mt-16">
            <motion.div
              className="inline-flex flex-col sm:flex-row items-center gap-4"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <p className="text-gray-600">
                Want to join our amazing team?
              </p>
              <motion.a
                href="/careers"
                className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                View Open Positions
                <motion.span
                  className="inline-block ml-2"
                  animate={{ x: [0, 2, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.span>
              </motion.a>
            </motion.div>
          </div>
        </RevealText>


      </div>
    </section>
  );
};

export default TeamSection;
