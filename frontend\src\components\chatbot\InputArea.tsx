'use client';

import React, { useState, useRef, useEffect } from 'react';
import { IoSend, IoImage, IoMic, IoMicOff, IoHappy } from 'react-icons/io5';
import { QuickReply } from '../../services/chatbotService';

interface InputAreaProps {
  onSendMessage: (text: string) => void;
  onSendImage?: (file: File) => void;
  onSendVoice?: (audioBlob: Blob) => void;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  quickReplies?: QuickReply[];
  onQuickReplyClick?: (reply: QuickReply) => void;
}

const InputArea: React.FC<InputAreaProps> = ({
  onSendMessage,
  onSendImage,
  onSendVoice,
  isLoading = false,
  placeholder = 'Type a message...',
  disabled = false,
  maxLength = 500,
  quickReplies = [],
  onQuickReplyClick,
}) => {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto';
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Clear recording timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      if (mediaRecorderRef.current && isRecording) {
        mediaRecorderRef.current.stop();
      }
    };
  }, [isRecording]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (e.target.value.length <= maxLength) {
      setMessage(e.target.value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = () => {
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message);
      setMessage('');
      inputRef.current?.focus();
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0] && onSendImage) {
      onSendImage(e.target.files[0]);
      e.target.value = ''; // Clear the input
    }
  };

  const triggerImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage((prev) => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  };

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  const toggleVoiceRecording = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      
      mediaRecorderRef.current.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };
      
      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        
        if (onSendVoice) {
          onSendVoice(audioBlob);
        }
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
        
        // Reset recording state
        setIsRecording(false);
        setRecordingTime(0);
        
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };
      
      mediaRecorderRef.current.start();
      setIsRecording(true);
      
      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
    }
  };

  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-3">
      {quickReplies.length > 0 && (
        <div className="pb-3 flex flex-wrap gap-2 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300">
          {quickReplies.map((reply) => (
            <button
              key={reply.id}
              onClick={() => onQuickReplyClick?.(reply)}
              className="bg-secondary/10 hover:bg-secondary/20 text-secondary px-3 py-1 rounded-full text-sm transition-colors whitespace-nowrap"
            >
              {reply.text}
            </button>
          ))}
        </div>
      )}
      
      <div className="relative">
        {/* Emoji picker (simplified - in production you'd use a proper emoji picker library) */}
        {showEmojiPicker && (
          <div className="absolute bottom-full mb-2 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 p-2 z-10">
            <div className="grid grid-cols-8 gap-1">
              {[
                { emoji: '😀', icon: '😊' },
                { emoji: '😍', icon: '💕' },
                { emoji: '😂', icon: '😄' },
                { emoji: '👍', icon: '👍' },
                { emoji: '❤️', icon: '♥' },
                { emoji: '😊', icon: '😊' },
                { emoji: '🙏', icon: '🙏' },
                { emoji: '🎉', icon: '🎊' },
                { emoji: '🔥', icon: '🔥' },
                { emoji: '💯', icon: '💯' },
                { emoji: '👏', icon: '👏' },
                { emoji: '😎', icon: '😎' },
                { emoji: '🤔', icon: '🤔' },
                { emoji: '👋', icon: '👋' },
                { emoji: '🙌', icon: '🙌' },
                { emoji: '✨', icon: '✨' }
              ].map(({ emoji }) => (
                <button
                  key={emoji}
                  onClick={() => handleEmojiSelect(emoji)}
                  className="w-8 h-8 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors flex items-center justify-center text-xl"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="flex items-end rounded-lg border border-gray-300 dark:border-gray-600 focus-within:border-primary focus-within:ring-1 focus-within:ring-primary">
          <textarea
            ref={inputRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={isRecording ? 'Recording...' : placeholder}
            disabled={disabled || isLoading || isRecording}
            className="flex-1 max-h-32 border-0 bg-transparent py-2 px-3 focus:ring-0 resize-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            rows={1}
            aria-label="Message input"
          />
          
          <div className="flex items-center space-x-1 p-2">
            {isRecording && (
              <div className="bg-red-500 rounded-full text-white text-xs px-2 py-1 flex items-center mr-2">
                <span className="mr-1 h-2 w-2 rounded-full bg-white animate-pulse"></span>
                {formatRecordingTime(recordingTime)}
              </div>
            )}
            
            {message.length > 0 && (
              <span className="text-xs text-gray-500 mr-1">
                {message.length}/{maxLength}
              </span>
            )}
            
            {onSendImage && (
              <button
                type="button"
                onClick={triggerImageUpload}
                disabled={isLoading || disabled || isRecording}
                className="text-gray-500 hover:text-primary p-1 rounded-full transition-colors disabled:opacity-50"
                aria-label="Attach image"
              >
                <IoImage size={20} />
              </button>
            )}
            
            <button
              type="button"
              onClick={toggleEmojiPicker}
              disabled={isLoading || disabled || isRecording}
              className="text-gray-500 hover:text-primary p-1 rounded-full transition-colors disabled:opacity-50"
              aria-label="Emoji picker"
            >
              <IoHappy size={20} />
            </button>
            
            {onSendVoice && (
              <button
                type="button"
                onClick={toggleVoiceRecording}
                disabled={isLoading || disabled}
                className={`p-1 rounded-full transition-colors disabled:opacity-50 ${
                  isRecording ? 'text-red-500 hover:text-red-600' : 'text-gray-500 hover:text-primary'
                }`}
                aria-label={isRecording ? 'Stop recording' : 'Start voice recording'}
              >
                {isRecording ? <IoMicOff size={20} /> : <IoMic size={20} />}
              </button>
            )}
            
            <button
              type="button"
              onClick={handleSend}
              disabled={!message.trim() || isLoading || disabled || isRecording}
              className={`p-1 rounded-full transition-colors ${
                message.trim() && !isLoading && !disabled && !isRecording
                  ? 'text-primary hover:bg-primary/10'
                  : 'text-gray-400 cursor-not-allowed'
              }`}
              aria-label="Send message"
            >
              <IoSend size={20} />
            </button>
          </div>
        </div>
      </div>
      
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
        aria-hidden="true"
      />
    </div>
  );
};

export default InputArea; 