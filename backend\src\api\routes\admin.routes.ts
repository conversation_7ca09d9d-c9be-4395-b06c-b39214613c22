import { Router } from 'express';
import { 
  getApi<PERSON>eys,
  update<PERSON>pi<PERSON><PERSON>,
  testApi<PERSON>ey,
  getSystemConfig,
  updateSystemConfig,
  getDashboardStats,
  getWhatsAppConfig,
  updateWhatsAppConfig,
  getChatbotConfig,
  updateChatbotConfig,
  getPaymentConfig,
  updatePaymentConfig,
  getVoiceConfig,
  updateVoiceConfig,
  getEmailConfig,
  updateEmailConfig,
  getSEOConfig,
  updateSEOConfig,
  getInstagramConfig,
  updateInstagramConfig,
  exportConfig,
  importConfig,
  getSystemHealth,
  getAPIUsageStats,
  syncProductsToStripe
} from '../controllers/admin.controller';
import { authenticate } from '../../middlewares/auth';
import { requireAdmin } from '../../middlewares/requireAdmin';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(requireAdmin);

// Dashboard stats
router.get('/dashboard/stats', getDashboardStats);
router.get('/system/health', getSystemHealth);
router.get('/api/usage-stats', getAPIUsageStats);

// API Key Management
router.get('/api-keys', getApiKeys);
router.put('/api-keys/:service', updateApiKey);
router.post('/api-keys/:service/test', testApiKey);

// System Configuration
router.get('/config/system', getSystemConfig);
router.put('/config/system', updateSystemConfig);

// WhatsApp Configuration
router.get('/config/whatsapp', getWhatsAppConfig);
router.put('/config/whatsapp', updateWhatsAppConfig);

// Chatbot Configuration
router.get('/config/chatbot', getChatbotConfig);
router.put('/config/chatbot', updateChatbotConfig);

// Payment Configuration
router.get('/config/payment', getPaymentConfig);
router.put('/config/payment', updatePaymentConfig);

// Voice Processing Configuration
router.get('/config/voice', getVoiceConfig);
router.put('/config/voice', updateVoiceConfig);

// Email Configuration
router.get('/config/email', getEmailConfig);
router.put('/config/email', updateEmailConfig);

// SEO Configuration
router.get('/config/seo', getSEOConfig);
router.put('/config/seo', updateSEOConfig);

// Instagram Configuration
router.get('/config/instagram', getInstagramConfig);
router.put('/config/instagram', updateInstagramConfig);

// Configuration Import/Export
router.get('/config/export', exportConfig);
router.post('/config/import', importConfig);

// Stripe Integration
router.post('/stripe/sync-products', syncProductsToStripe);

export default router;