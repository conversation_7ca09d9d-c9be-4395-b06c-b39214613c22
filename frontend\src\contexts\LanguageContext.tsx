'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { 
  Locale, 
  defaultLocale, 
  locales, 
  detectUserLanguage, 
  addLocaleToPathname, 
  removeLocaleFromPathname,
  getLocaleFromPathname 
} from '@/i18n/config';

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  isLoading: boolean;
  availableLocales: readonly Locale[];
  switchLanguage: (locale: Locale) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(defaultLocale);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = () => {
      // Check for lang parameter in URL
      const urlLang = searchParams.get('lang') as Locale;

      // Check localStorage for saved preference
      const savedLocale = localStorage.getItem('preferred-locale') as Locale;

      // Use URL lang if present and valid, otherwise use saved locale or default
      const finalLocale = urlLang && locales.includes(urlLang) ? urlLang :
                         (savedLocale && locales.includes(savedLocale) ? savedLocale : defaultLocale);

      setLocaleState(finalLocale);
      localStorage.setItem('preferred-locale', finalLocale);
      setIsLoading(false);
    };

    initializeLanguage();
  }, [searchParams]);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem('preferred-locale', newLocale);

    // Update URL with lang parameter
    const currentParams = new URLSearchParams(searchParams.toString());
    if (newLocale === defaultLocale) {
      currentParams.delete('lang');
    } else {
      currentParams.set('lang', newLocale);
    }

    const newUrl = pathname + (currentParams.toString() ? `?${currentParams.toString()}` : '');
    router.push(newUrl);
  };

  const switchLanguage = (newLocale: Locale) => {
    if (newLocale === locale) return;

    setIsLoading(true);
    setLocale(newLocale);

    // Small delay to show loading state
    setTimeout(() => {
      setIsLoading(false);
    }, 300);
  };

  const value: LanguageContextType = {
    locale,
    setLocale,
    isLoading,
    availableLocales: locales,
    switchLanguage
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Language switcher component
export function LanguageSwitcher({ className = '' }: { className?: string }) {
  const { locale, switchLanguage, isLoading, availableLocales } = useLanguage();

  const languageNames = {
    en: 'English',
    es: 'Español'
  };

  const languageFlags = {
    en: '🇺🇸',
    es: '🇪🇸'
  };

  return (
    <div className={`relative ${className}`}>
      <select
        value={locale}
        onChange={(e) => switchLanguage(e.target.value as Locale)}
        disabled={isLoading}
        className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {availableLocales.map((lang) => (
          <option key={lang} value={lang}>
            {languageFlags[lang]} {languageNames[lang]}
          </option>
        ))}
      </select>
      
      {/* Custom dropdown arrow */}
      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}

// Compact language switcher for mobile
export function CompactLanguageSwitcher({ className = '' }: { className?: string }) {
  const { locale, switchLanguage, isLoading } = useLanguage();

  const languageFlags = {
    en: '🇺🇸',
    es: '🇪🇸'
  };

  const toggleLanguage = () => {
    const newLocale = locale === 'en' ? 'es' : 'en';
    switchLanguage(newLocale);
  };

  return (
    <button
      onClick={toggleLanguage}
      disabled={isLoading}
      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      title={`Switch to ${locale === 'en' ? 'Español' : 'English'}`}
    >
      <span className="text-lg">{languageFlags[locale]}</span>
      <span className="hidden sm:inline">{locale.toUpperCase()}</span>
      
      {isLoading && (
        <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
      )}
    </button>
  );
}
